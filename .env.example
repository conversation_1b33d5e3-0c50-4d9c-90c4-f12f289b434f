# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
LOG_FORMAT=combined

# Zalo Configuration
ZALO_APP_ID=your_zalo_app_id
ZALO_SECRET_KEY=your_zalo_secret_key
ZALO_OA_ID=your_zalo_oa_id
ZALO_WEBHOOK_URL=https://your-domain.com/webhook

# Chatwoot Integration
# Bật/tắt tích hợp Chatwoot (true/false)
CHATWOOT_ENABLED=true

# URL của Chatwoot instance
# Ví dụ: https://app.chatwoot.com hoặc https://your-chatwoot-domain.com
CHATWOOT_BASE_URL=https://app.chatwoot.com

# API Access Token từ Chatwoot
# Lấy từ: Profile Settings → Access Token
CHATWOOT_API_ACCESS_TOKEN=your_api_access_token_here

# Account ID từ URL dashboard
# Ví dụ: https://app.chatwoot.com/app/accounts/213/dashboard (Account ID = 213)
CHATWOOT_ACCOUNT_ID=213

# Inbox ID của API channel
# Lấy từ: Settings → Inboxes → API Channel → Settings → Configuration
CHATWOOT_INBOX_ID=281

# Webhook Configuration
# URL webhook để gửi phản hồi tự động
WEBHOOK_URL=https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982
