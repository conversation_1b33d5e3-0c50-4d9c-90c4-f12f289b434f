import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { zaloSessionManager } from './ZaloSessionManager';

export interface MonitoringMetrics {
  totalAccounts: number;
  activeAccounts: number;
  inactiveAccounts: number;
  errorAccounts: number;
  expiredAccounts: number;
  healthyConnections: number;
  unhealthyConnections: number;
  averageResponseTime: number;
  uptime: number;
  lastUpdate: Date;
}

export interface AccountMetrics {
  accountId: string;
  tenantId: string;
  zaloUserId: string;
  status: string;
  isHealthy: boolean;
  lastActivity: Date;
  responseTime: number;
  errorCount: number;
  uptime: number;
  chatwootStatus: string;
}

export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number; // Percentage
    responseTime: number; // Milliseconds
    downtime: number; // Minutes
    unhealthyAccounts: number; // Count
  };
  notifications: {
    email?: string[];
    webhook?: string;
    slack?: string;
  };
}

export class ZaloMonitoringService {
  private metrics: MonitoringMetrics;
  private accountMetrics: Map<string, AccountMetrics> = new Map();
  private alertConfig: AlertConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private startTime: Date;

  constructor() {
    this.startTime = new Date();
    this.metrics = this.initializeMetrics();
    this.alertConfig = this.getDefaultAlertConfig();
    this.startMonitoring();
  }

  private initializeMetrics(): MonitoringMetrics {
    return {
      totalAccounts: 0,
      activeAccounts: 0,
      inactiveAccounts: 0,
      errorAccounts: 0,
      expiredAccounts: 0,
      healthyConnections: 0,
      unhealthyConnections: 0,
      averageResponseTime: 0,
      uptime: 0,
      lastUpdate: new Date()
    };
  }

  private getDefaultAlertConfig(): AlertConfig {
    return {
      enabled: true,
      thresholds: {
        errorRate: 20, // 20% error rate
        responseTime: 5000, // 5 seconds
        downtime: 10, // 10 minutes
        unhealthyAccounts: 3 // 3 unhealthy accounts
      },
      notifications: {
        email: [],
        webhook: process.env.MONITORING_WEBHOOK_URL,
        slack: process.env.SLACK_WEBHOOK_URL
      }
    };
  }

  /**
   * Bắt đầu monitoring
   */
  private startMonitoring(): void {
    logger.info('Starting Zalo monitoring service');

    // Update metrics every 2 minutes
    this.monitoringInterval = setInterval(async () => {
      await this.updateMetrics();
      await this.checkAlerts();
    }, 2 * 60 * 1000);

    // Initial metrics update
    setTimeout(() => this.updateMetrics(), 5000);
  }

  /**
   * Dừng monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Zalo monitoring service stopped');
    }
  }

  /**
   * Cập nhật metrics
   */
  private async updateMetrics(): Promise<void> {
    try {
      logger.debug('Updating monitoring metrics');

      // Get all accounts from database
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('id, tenant_id, zalo_user_id, status, last_activity_at, chatwoot_channel_status, error_data, expires_at');

      if (error) {
        logger.error('Failed to fetch accounts for monitoring', { error: error.message });
        return;
      }

      // Reset metrics
      this.metrics = {
        ...this.initializeMetrics(),
        lastUpdate: new Date(),
        uptime: Math.floor((Date.now() - this.startTime.getTime()) / 1000)
      };

      // Update account metrics
      const responseTimes: number[] = [];
      
      for (const account of accounts || []) {
        const startTime = Date.now();
        
        // Get health status from session manager
        const health = zaloSessionManager.getAccountHealth(account.id);
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);

        // Update account metrics
        const accountMetric: AccountMetrics = {
          accountId: account.id,
          tenantId: account.tenant_id,
          zaloUserId: account.zalo_user_id,
          status: account.status,
          isHealthy: health?.isHealthy || false,
          lastActivity: account.last_activity_at ? new Date(account.last_activity_at) : new Date(0),
          responseTime,
          errorCount: health?.errorCount || 0,
          uptime: this.calculateUptime(account.last_activity_at),
          chatwootStatus: account.chatwoot_channel_status || 'inactive'
        };

        this.accountMetrics.set(account.id, accountMetric);

        // Update global metrics
        this.metrics.totalAccounts++;
        
        switch (account.status) {
          case 'active':
            this.metrics.activeAccounts++;
            break;
          case 'inactive':
            this.metrics.inactiveAccounts++;
            break;
          case 'error':
            this.metrics.errorAccounts++;
            break;
          case 'expired':
            this.metrics.expiredAccounts++;
            break;
        }

        if (health?.isHealthy) {
          this.metrics.healthyConnections++;
        } else {
          this.metrics.unhealthyConnections++;
        }
      }

      // Calculate average response time
      if (responseTimes.length > 0) {
        this.metrics.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      }

      logger.debug('Monitoring metrics updated', {
        totalAccounts: this.metrics.totalAccounts,
        activeAccounts: this.metrics.activeAccounts,
        healthyConnections: this.metrics.healthyConnections,
        averageResponseTime: this.metrics.averageResponseTime
      });

    } catch (error: any) {
      logger.error('Failed to update monitoring metrics', { error: error.message });
    }
  }

  /**
   * Tính uptime cho account
   */
  private calculateUptime(lastActivityAt: string | null): number {
    if (!lastActivityAt) return 0;
    
    const lastActivity = new Date(lastActivityAt);
    const now = new Date();
    const diffMs = now.getTime() - lastActivity.getTime();
    
    // Return uptime percentage in last 24 hours
    const dayMs = 24 * 60 * 60 * 1000;
    return Math.max(0, Math.min(100, ((dayMs - diffMs) / dayMs) * 100));
  }

  /**
   * Kiểm tra alerts
   */
  private async checkAlerts(): Promise<void> {
    if (!this.alertConfig.enabled) return;

    try {
      const alerts: string[] = [];

      // Check error rate
      const errorRate = (this.metrics.errorAccounts / Math.max(1, this.metrics.totalAccounts)) * 100;
      if (errorRate > this.alertConfig.thresholds.errorRate) {
        alerts.push(`High error rate: ${errorRate.toFixed(1)}% (threshold: ${this.alertConfig.thresholds.errorRate}%)`);
      }

      // Check response time
      if (this.metrics.averageResponseTime > this.alertConfig.thresholds.responseTime) {
        alerts.push(`High response time: ${this.metrics.averageResponseTime.toFixed(0)}ms (threshold: ${this.alertConfig.thresholds.responseTime}ms)`);
      }

      // Check unhealthy accounts
      if (this.metrics.unhealthyConnections > this.alertConfig.thresholds.unhealthyAccounts) {
        alerts.push(`Too many unhealthy accounts: ${this.metrics.unhealthyConnections} (threshold: ${this.alertConfig.thresholds.unhealthyAccounts})`);
      }

      // Send alerts if any
      if (alerts.length > 0) {
        await this.sendAlerts(alerts);
      }

    } catch (error: any) {
      logger.error('Failed to check alerts', { error: error.message });
    }
  }

  /**
   * Gửi alerts
   */
  private async sendAlerts(alerts: string[]): Promise<void> {
    const alertMessage = {
      service: 'Zalo Authentication',
      timestamp: new Date().toISOString(),
      alerts: alerts,
      metrics: this.metrics,
      severity: 'warning'
    };

    logger.warn('Zalo monitoring alerts triggered', alertMessage);

    // Send to webhook if configured
    if (this.alertConfig.notifications.webhook) {
      try {
        const response = await fetch(this.alertConfig.notifications.webhook, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alertMessage)
        });

        if (!response.ok) {
          logger.error('Failed to send webhook alert', { 
            status: response.status,
            statusText: response.statusText 
          });
        }
      } catch (error: any) {
        logger.error('Webhook alert failed', { error: error.message });
      }
    }

    // Send to Slack if configured
    if (this.alertConfig.notifications.slack) {
      try {
        const slackMessage = {
          text: `🚨 Zalo Authentication Alerts`,
          attachments: [{
            color: 'warning',
            fields: alerts.map(alert => ({
              title: 'Alert',
              value: alert,
              short: false
            }))
          }]
        };

        const response = await fetch(this.alertConfig.notifications.slack, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(slackMessage)
        });

        if (!response.ok) {
          logger.error('Failed to send Slack alert', { 
            status: response.status,
            statusText: response.statusText 
          });
        }
      } catch (error: any) {
        logger.error('Slack alert failed', { error: error.message });
      }
    }
  }

  /**
   * Lấy metrics hiện tại
   */
  getMetrics(): MonitoringMetrics {
    return { ...this.metrics };
  }

  /**
   * Lấy metrics của account cụ thể
   */
  getAccountMetrics(accountId: string): AccountMetrics | null {
    return this.accountMetrics.get(accountId) || null;
  }

  /**
   * Lấy tất cả account metrics
   */
  getAllAccountMetrics(): AccountMetrics[] {
    return Array.from(this.accountMetrics.values());
  }

  /**
   * Lấy metrics theo tenant
   */
  getMetricsByTenant(tenantId: string): {
    metrics: Partial<MonitoringMetrics>;
    accounts: AccountMetrics[];
  } {
    const tenantAccounts = Array.from(this.accountMetrics.values())
      .filter(account => account.tenantId === tenantId);

    const tenantMetrics = {
      totalAccounts: tenantAccounts.length,
      activeAccounts: tenantAccounts.filter(a => a.status === 'active').length,
      inactiveAccounts: tenantAccounts.filter(a => a.status === 'inactive').length,
      errorAccounts: tenantAccounts.filter(a => a.status === 'error').length,
      healthyConnections: tenantAccounts.filter(a => a.isHealthy).length,
      unhealthyConnections: tenantAccounts.filter(a => !a.isHealthy).length,
      averageResponseTime: tenantAccounts.length > 0 
        ? tenantAccounts.reduce((sum, a) => sum + a.responseTime, 0) / tenantAccounts.length 
        : 0,
      lastUpdate: this.metrics.lastUpdate
    };

    return {
      metrics: tenantMetrics,
      accounts: tenantAccounts
    };
  }

  /**
   * Cập nhật alert config
   */
  updateAlertConfig(config: Partial<AlertConfig>): void {
    this.alertConfig = { ...this.alertConfig, ...config };
    logger.info('Alert configuration updated', this.alertConfig);
  }

  /**
   * Force update metrics
   */
  async forceUpdateMetrics(): Promise<void> {
    await this.updateMetrics();
  }
}

// Export singleton instance
export const zaloMonitoringService = new ZaloMonitoringService();
