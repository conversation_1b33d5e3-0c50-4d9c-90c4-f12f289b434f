// Load environment variables first
import * as dotenv from 'dotenv';
dotenv.config();

import { ZaloBot } from './services/ZaloBot';
import { config } from './config/config';
import { logger } from './utils/logger';
import ExpressApp from './app';

// Declare Node.js globals
declare const process: any;

async function main(): Promise<void> {
  try {
    logger.info('🚀 Starting Zalo Chatbot with Express API...');

    // Start Express API server
    const port = process.env.PORT || 3000;
    const expressApp = new ExpressApp(Number(port));
    expressApp.listen();

    // Initialize Zalo Bot
    const bot = new ZaloBot(config.zalo);
    await bot.initialize();

    logger.info('✅ Zalo Chatbot and Express API started successfully!');
    logger.info(`📍 API available at: http://localhost:${port}`);
    logger.info(`📍 Test endpoints: http://localhost:${port}/api/test`);
  } catch (error) {
    logger.error('❌ Failed to start application:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('🛑 Shutting down gracefully...');
  process.exit(0);
});

// Start the application
main().catch(error => {
  logger.error('💥 Unhandled error:', error);
  process.exit(1);
});
