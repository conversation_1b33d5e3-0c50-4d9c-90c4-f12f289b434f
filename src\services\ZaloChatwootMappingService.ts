import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';

// Interface cho mapping data
export interface ZaloChatwootMapping {
  id?: string;
  tenant_id: string;
  zalo_thread_id: string;
  zalo_user_id: string;
  zalo_user_name?: string;
  chatwoot_contact_id: number;
  chatwoot_conversation_id?: number;
  chatwoot_inbox_id: number;
  chatwoot_source_id: string;
  is_active?: boolean;
  last_message_at?: string;
  message_count?: number;
  metadata?: any;
  created_at?: string;
  updated_at?: string;
}

export interface CreateMappingData {
  tenant_id: string;
  zalo_thread_id: string;
  zalo_user_id: string;
  zalo_user_name?: string;
  chatwoot_contact_id: number;
  chatwoot_conversation_id?: number;
  chatwoot_inbox_id: number;
  chatwoot_source_id: string;
  metadata?: any;
}

export interface UpdateMappingData {
  chatwoot_conversation_id?: number;
  zalo_user_name?: string;
  is_active?: boolean;
  last_message_at?: string;
  message_count?: number;
  metadata?: any;
}

export class ZaloChatwootMappingService {
  /**
   * Set tenant context cho service role
   */
  private async setTenantContext(tenantId: string): Promise<void> {
    try {
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });
    } catch (error) {
      logger.warn('Failed to set tenant context', { tenantId, error });
    }
  }

  /**
   * Tìm mapping theo Zalo thread ID
   */
  async findByZaloThreadId(tenantId: string, zaloThreadId: string): Promise<ZaloChatwootMapping | null> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Finding mapping by Zalo thread ID', {
        tenantId,
        zaloThreadId
      });

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('zalo_thread_id', zaloThreadId)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found
          logger.info('No mapping found for Zalo thread ID', {
            tenantId,
            zaloThreadId
          });
          return null;
        }
        throw error;
      }

      logger.info('Found mapping by Zalo thread ID', {
        mappingId: data.id,
        chatwootContactId: data.chatwoot_contact_id,
        chatwootConversationId: data.chatwoot_conversation_id
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to find mapping by Zalo thread ID', {
        tenantId,
        zaloThreadId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Tìm mapping theo Zalo user ID
   */
  async findByZaloUserId(tenantId: string, zaloUserId: string): Promise<ZaloChatwootMapping[]> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Finding mappings by Zalo user ID', {
        tenantId,
        zaloUserId
      });

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('zalo_user_id', zaloUserId)
        .eq('is_active', true)
        .order('last_message_at', { ascending: false });

      if (error) {
        throw error;
      }

      logger.info('Found mappings by Zalo user ID', {
        tenantId,
        zaloUserId,
        count: data?.length || 0
      });

      return data || [];
    } catch (error: any) {
      logger.error('Failed to find mappings by Zalo user ID', {
        tenantId,
        zaloUserId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Tìm mapping theo Chatwoot contact ID
   */
  async findByChatwootContactId(tenantId: string, chatwootContactId: number): Promise<ZaloChatwootMapping[]> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Finding mappings by Chatwoot contact ID', {
        tenantId,
        chatwootContactId
      });

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .select('*')
        .eq('tenant_id', tenantId)
        .eq('chatwoot_contact_id', chatwootContactId)
        .eq('is_active', true)
        .order('last_message_at', { ascending: false });

      if (error) {
        throw error;
      }

      logger.info('Found mappings by Chatwoot contact ID', {
        tenantId,
        chatwootContactId,
        count: data?.length || 0
      });

      return data || [];
    } catch (error: any) {
      logger.error('Failed to find mappings by Chatwoot contact ID', {
        tenantId,
        chatwootContactId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Tạo mapping mới
   */
  async createMapping(mappingData: CreateMappingData): Promise<ZaloChatwootMapping> {
    try {
      await this.setTenantContext(mappingData.tenant_id);

      logger.info('Creating new Zalo-Chatwoot mapping', {
        tenantId: mappingData.tenant_id,
        zaloThreadId: mappingData.zalo_thread_id,
        zaloUserId: mappingData.zalo_user_id,
        chatwootContactId: mappingData.chatwoot_contact_id
      });

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .insert({
          ...mappingData,
          last_message_at: new Date().toISOString(),
          message_count: 1
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Created new mapping successfully', {
        mappingId: data.id,
        tenantId: data.tenant_id,
        zaloThreadId: data.zalo_thread_id
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to create mapping', {
        mappingData,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Cập nhật mapping
   */
  async updateMapping(
    tenantId: string,
    zaloThreadId: string,
    updateData: UpdateMappingData
  ): Promise<ZaloChatwootMapping> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Updating Zalo-Chatwoot mapping', {
        tenantId,
        zaloThreadId,
        updateData
      });

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('tenant_id', tenantId)
        .eq('zalo_thread_id', zaloThreadId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info('Updated mapping successfully', {
        mappingId: data.id,
        tenantId: data.tenant_id,
        zaloThreadId: data.zalo_thread_id
      });

      return data;
    } catch (error: any) {
      logger.error('Failed to update mapping', {
        tenantId,
        zaloThreadId,
        updateData,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Tăng message count và cập nhật last_message_at
   */
  async incrementMessageCount(tenantId: string, zaloThreadId: string): Promise<void> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Incrementing message count', {
        tenantId,
        zaloThreadId
      });

      // Lấy current count trước
      const { data: currentMapping } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .select('message_count')
        .eq('tenant_id', tenantId)
        .eq('zalo_thread_id', zaloThreadId)
        .single();

      const newCount = (currentMapping?.message_count || 0) + 1;

      const { error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .update({
          message_count: newCount,
          last_message_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('tenant_id', tenantId)
        .eq('zalo_thread_id', zaloThreadId);

      if (error) {
        throw error;
      }

      logger.info('Incremented message count successfully', {
        tenantId,
        zaloThreadId
      });
    } catch (error: any) {
      logger.error('Failed to increment message count', {
        tenantId,
        zaloThreadId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Deactivate mapping
   */
  async deactivateMapping(tenantId: string, zaloThreadId: string): Promise<void> {
    try {
      await this.setTenantContext(tenantId);

      logger.info('Deactivating mapping', {
        tenantId,
        zaloThreadId
      });

      const { error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('tenant_id', tenantId)
        .eq('zalo_thread_id', zaloThreadId);

      if (error) {
        throw error;
      }

      logger.info('Deactivated mapping successfully', {
        tenantId,
        zaloThreadId
      });
    } catch (error: any) {
      logger.error('Failed to deactivate mapping', {
        tenantId,
        zaloThreadId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy thống kê mapping
   */
  async getMappingStats(tenantId: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    totalMessages: number;
  }> {
    try {
      await this.setTenantContext(tenantId);

      const { data, error } = await supabaseAdmin
        .from('zalo_chatwoot_mapping')
        .select('is_active, message_count')
        .eq('tenant_id', tenantId);

      if (error) {
        throw error;
      }

      const stats = {
        total: data.length,
        active: data.filter(m => m.is_active).length,
        inactive: data.filter(m => !m.is_active).length,
        totalMessages: data.reduce((sum, m) => sum + (m.message_count || 0), 0)
      };

      logger.info('Retrieved mapping stats', {
        tenantId,
        stats
      });

      return stats;
    } catch (error: any) {
      logger.error('Failed to get mapping stats', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }
}

// Export singleton instance
export const zaloChatwootMappingService = new ZaloChatwootMappingService();
