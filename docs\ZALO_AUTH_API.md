# Zalo Authentication API Documentation

Hệ thống API để quản lý đăng nhập <PERSON>alo cho multi-tenant với tích hợp Chatwoot.

## Tổng quan

API này cung cấp các chức năng:
- **QR Code Login**: Tạo và quản lý QR code để đăng nhập Zalo
- **<PERSON>ie Login**: <PERSON><PERSON><PERSON> nhập bằng cookies đã có
- **Account Management**: Quản lý tài khoản Zalo
- **Session Management**: <PERSON> dõi và duy trì sessions
- **Chatwoot Integration**: Liên kết với Chatwoot inboxes
- **Health Monitoring**: Giám sát trạng thái kết nối

## Base URL

```
https://your-domain.com/api/zalo-auth
```

## Authentication

Tất cả API endpoints yêu cầu:
- **Tenant ID**: <PERSON><PERSON><PERSON><PERSON> gửi qua header `X-Tenant-ID` hoặc query parameter `tenant_id`
- **Rate Limiting**: Áp dụng cho các operations nhạy cảm

## API Endpoints

### 1. Generate QR Code

Tạo QR code để đăng nhập Zalo account mới.

```http
POST /generate-qr
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
Content-Type: application/json
```

**Request Body:**
```json
{
  "expires_in_minutes": 10,
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
}
```

**Response:**
```json
{
  "success": true,
  "message": "QR code generated successfully",
  "data": {
    "account_id": "uuid",
    "qr_session_id": "uuid",
    "expires_at": "2024-01-01T12:00:00Z",
    "status": "pending",
    "qr_code_url": "/api/zalo-auth/qr-image/session-id"
  }
}
```

**Rate Limit:** 10 requests per 15 minutes per tenant

### 2. Get QR Code Image

Lấy hình ảnh QR code.

```http
GET /qr-image/:sessionId
```

**Response:** PNG image

### 3. Check QR Status

Kiểm tra trạng thái QR code và quá trình đăng nhập.

```http
GET /qr-status/:accountId
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "QR status retrieved successfully",
  "data": {
    "status": "confirmed|pending|expired|failed",
    "account": {
      "id": "uuid",
      "zalo_user_id": "string",
      "zalo_display_name": "string",
      "status": "active|inactive|pending",
      "qr_data": {
        "qr_status": "confirmed",
        "qr_expires_at": "2024-01-01T12:00:00Z"
      }
    }
  }
}
```

### 4. Login with Cookie

Đăng nhập bằng cookies Zalo đã có.

```http
POST /login-cookie
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
Content-Type: application/json
```

**Request Body:**
```json
{
  "cookies": {
    // Zalo cookies object
  },
  "imei": "device-imei",
  "user_agent": "browser-user-agent",
  "zalo_user_id": "optional-zalo-user-id",
  "zalo_display_name": "optional-display-name"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "account": {
      "id": "uuid",
      "zalo_user_id": "string",
      "zalo_display_name": "string",
      "zalo_phone": "string",
      "status": "active",
      "chatwoot_inbox_id": null,
      "chatwoot_channel_status": "inactive",
      "last_login_at": "2024-01-01T12:00:00Z",
      "expires_at": "2024-01-31T12:00:00Z"
    }
  }
}
```

**Rate Limit:** 30 requests per 5 minutes per tenant

### 5. List Accounts

Lấy danh sách tài khoản Zalo của tenant.

```http
GET /accounts?status=active&limit=50&offset=0
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Query Parameters:**
- `status` (optional): Filter by status (active, inactive, expired, error)
- `limit` (optional): Number of results (default: 50, max: 100)
- `offset` (optional): Pagination offset (default: 0)

**Response:**
```json
{
  "success": true,
  "message": "Accounts retrieved successfully",
  "data": {
    "accounts": [
      {
        "id": "uuid",
        "zalo_user_id": "string",
        "zalo_display_name": "string",
        "status": "active",
        "chatwoot_inbox_id": 123,
        "chatwoot_channel_status": "active",
        "login_method": "qr|cookie",
        "last_login_at": "2024-01-01T12:00:00Z",
        "last_activity_at": "2024-01-01T12:30:00Z",
        "expires_at": "2024-01-31T12:00:00Z",
        "health": {
          "is_healthy": true,
          "last_check": "2024-01-01T12:30:00Z",
          "error_count": 0,
          "last_error": null
        }
      }
    ],
    "pagination": {
      "total": 10,
      "limit": 50,
      "offset": 0,
      "has_more": false
    }
  }
}
```

### 6. Get Account Details

Lấy chi tiết một tài khoản cụ thể.

```http
GET /account/:accountId
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "Account status retrieved successfully",
  "data": {
    "account_id": "uuid",
    "zalo_user_id": "string",
    "status": "active",
    "is_connected": true,
    "last_activity_at": "2024-01-01T12:30:00Z",
    "expires_at": "2024-01-31T12:00:00Z",
    "chatwoot_status": "active",
    "error_message": null,
    "health": {
      "is_healthy": true,
      "last_check": "2024-01-01T12:30:00Z",
      "error_count": 0,
      "last_error": null
    }
  }
}
```

### 7. Refresh Session

Làm mới session của tài khoản.

```http
POST /refresh/:accountId
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "Session refreshed successfully",
  "data": {
    "account_id": "uuid",
    "zalo_user_id": "string",
    "status": "active",
    "is_connected": true,
    "last_activity_at": "2024-01-01T12:35:00Z"
  }
}
```

**Rate Limit:** 30 requests per 5 minutes per tenant

### 8. Remove Account

Xóa tài khoản Zalo.

```http
DELETE /account/:accountId
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "Account removed successfully",
  "data": {
    "account_id": "uuid",
    "removed_at": "2024-01-01T12:40:00Z"
  }
}
```

**Rate Limit:** 30 requests per 5 minutes per tenant

### 9. Health Status

Lấy trạng thái health của tất cả sessions.

```http
GET /health
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "Health status retrieved successfully",
  "data": {
    "stats": {
      "total_sessions": 10,
      "active_sessions": 8,
      "error_sessions": 1,
      "pending_sessions": 1,
      "healthy_connections": 7,
      "tenant_sessions": 5,
      "tenant_healthy": 4,
      "tenant_errors": 1
    },
    "sessions": [
      {
        "account_id": "uuid",
        "zalo_user_id": "string",
        "is_healthy": true,
        "last_check": "2024-01-01T12:30:00Z",
        "error_count": 0,
        "last_error": null
      }
    ]
  }
}
```

### 10. Force Health Check

Thực hiện health check cho một tài khoản cụ thể.

```http
POST /health-check/:accountId
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
```

**Response:**
```json
{
  "success": true,
  "message": "Health check completed",
  "data": {
    "account_id": "uuid",
    "zalo_user_id": "string",
    "is_healthy": true,
    "last_check": "2024-01-01T12:35:00Z",
    "error_count": 0,
    "last_error": null
  }
}
```

### 11. Link Chatwoot

Liên kết tài khoản Zalo với Chatwoot inbox.

```http
POST /link-chatwoot
```

**Headers:**
```
X-Tenant-ID: <tenant-uuid>
Content-Type: application/json
```

**Request Body:**
```json
{
  "account_id": "uuid",
  "chatwoot_inbox_id": 123,
  "chatwoot_source_id": "optional-custom-source-id"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chatwoot linked successfully",
  "data": {
    "account_id": "uuid",
    "zalo_user_id": "string",
    "chatwoot_inbox_id": 123,
    "chatwoot_source_id": "zalo_uuid",
    "chatwoot_channel_status": "active"
  }
}
```

## Error Responses

Tất cả lỗi trả về format:

```json
{
  "success": false,
  "message": "Error description",
  "error": {
    "code": "ERROR_CODE",
    "details": "Additional error details"
  }
}
```

### Common Error Codes

- `TENANT_ID_REQUIRED`: Missing tenant ID
- `ACCOUNT_NOT_FOUND`: Zalo account not found
- `ACCOUNT_NOT_ACTIVE`: Account is not in active state
- `ACCOUNT_EXPIRED`: Account session has expired
- `RATE_LIMIT_EXCEEDED`: Rate limit exceeded
- `QR_GENERATION_FAILED`: Failed to generate QR code
- `LOGIN_FAILED`: Login attempt failed
- `REFRESH_FAILED`: Session refresh failed
- `CHATWOOT_LINK_FAILED`: Failed to link Chatwoot

## Rate Limiting

### QR Generation
- **Limit**: 10 requests per 15 minutes per tenant
- **Scope**: Per tenant
- **Reset**: Rolling window

### Account Operations
- **Limit**: 30 requests per 5 minutes per tenant
- **Scope**: Per tenant (refresh, remove, health-check)
- **Reset**: Rolling window

### Rate Limit Headers

```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 9
X-RateLimit-Reset: **********
```

## Webhooks

Khi có tin nhắn từ Zalo, hệ thống sẽ tự động:
1. Xác định tài khoản Zalo tương ứng
2. Cập nhật activity timestamp
3. Xử lý tin nhắn qua Chatwoot (nếu được liên kết)
4. Update health status

## Best Practices

1. **Polling QR Status**: Poll mỗi 2-3 giây, dừng khi status != 'pending'
2. **Error Handling**: Implement retry logic với exponential backoff
3. **Health Monitoring**: Theo dõi health status để phát hiện sớm vấn đề
4. **Session Management**: Refresh sessions trước khi hết hạn
5. **Rate Limiting**: Implement client-side rate limiting để tránh 429 errors

## SDK Examples

### JavaScript/Node.js

```javascript
const ZaloAuthClient = require('./zalo-auth-client');

const client = new ZaloAuthClient({
  baseUrl: 'https://your-domain.com/api/zalo-auth',
  tenantId: 'your-tenant-id'
});

// Generate QR code
const qr = await client.generateQR({
  expires_in_minutes: 10
});

// Poll QR status
const pollQRStatus = async (accountId) => {
  const interval = setInterval(async () => {
    const status = await client.checkQRStatus(accountId);
    
    if (status.data.status === 'confirmed') {
      clearInterval(interval);
      console.log('Login successful!', status.data.account);
    } else if (status.data.status === 'expired') {
      clearInterval(interval);
      console.log('QR code expired');
    }
  }, 3000);
};

pollQRStatus(qr.data.account_id);
```

### Python

```python
import requests
import time

class ZaloAuthClient:
    def __init__(self, base_url, tenant_id):
        self.base_url = base_url
        self.tenant_id = tenant_id
        self.headers = {'X-Tenant-ID': tenant_id}
    
    def generate_qr(self, expires_in_minutes=10):
        response = requests.post(
            f"{self.base_url}/generate-qr",
            headers=self.headers,
            json={"expires_in_minutes": expires_in_minutes}
        )
        return response.json()
    
    def check_qr_status(self, account_id):
        response = requests.get(
            f"{self.base_url}/qr-status/{account_id}",
            headers=self.headers
        )
        return response.json()
    
    def poll_qr_status(self, account_id, timeout=600):
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.check_qr_status(account_id)
            
            if status['data']['status'] == 'confirmed':
                return status['data']['account']
            elif status['data']['status'] == 'expired':
                raise Exception('QR code expired')
            
            time.sleep(3)
        
        raise Exception('Timeout waiting for QR confirmation')

# Usage
client = ZaloAuthClient('https://your-domain.com/api/zalo-auth', 'your-tenant-id')
qr = client.generate_qr()
print(f"QR Code URL: {qr['data']['qr_code_url']}")

account = client.poll_qr_status(qr['data']['account_id'])
print(f"Login successful: {account['zalo_display_name']}")
```
