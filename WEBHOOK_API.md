# Zalo Webhook API Documentation

## Tổng quan

API này được thiết kế để nhận tin nhắn từ Zalo và tự động gửi phản hồi "Đã Nhận" đến webhook test c<PERSON><PERSON>.

## Endpoints

### 1. POST /api/zalo-webhook

**<PERSON><PERSON> tả:** Nhận tin nhắn từ Zalo và gửi phản hồi tự động

**URL:** `http://localhost:3000/api/zalo-webhook`

**Method:** POST

**Content-Type:** application/json

**Request Body:**
```json
{
  "type": 0,
  "data": {
    "actionId": "10800571650848",
    "msgId": "6824012833145",
    "cliMsgId": "1753014376324",
    "msgType": "webchat",
    "uidFrom": "4771680556510128931",
    "idTo": "1775737296024628671",
    "dName": "<PERSON><PERSON><PERSON> Trần",
    "ts": "1753014374857",
    "status": 1,
    "content": "alo",
    "notify": "1",
    "ttl": 0,
    "userId": "0",
    "uin": "0",
    "topOut": "0",
    "topOutTimeOut": "0",
    "topOutImprTimeOut": "0",
    "propertyExt": {
      "color": 0,
      "size": 0,
      "type": 0,
      "subType": 0,
      "ext": "{\"shouldParseLinkOrContact\": 0}"
    },
    "paramsExt": {
      "countUnread": 1,
      "containType": 0,
      "platformType": 2
    },
    "cmd": 501,
    "st": 3,
    "at": 5,
    "realMsgId": "0"
  },
  "threadId": "4771680556510128931",
  "isSelf": false
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Message received and auto-reply sent successfully",
  "data": {
    "receivedMessage": {
      "from": "Thọ Trần",
      "content": "alo",
      "messageId": "6824012833145",
      "timestamp": "1753014374857"
    },
    "autoReply": {
      "message": "Đã Nhận",
      "sentAt": "2025-01-20T10:30:45.123Z",
      "webhookStatus": 200
    },
    "webhookResponse": {
      "status": 200,
      "data": {
        // Response từ webhook Mooly
      }
    }
  }
}
```

**Response (Webhook Failed):**
```json
{
  "success": true,
  "message": "Message received but failed to send auto-reply",
  "data": {
    "receivedMessage": {
      "from": "Thọ Trần",
      "content": "alo",
      "messageId": "6824012833145",
      "timestamp": "1753014374857"
    },
    "autoReply": {
      "message": "Đã Nhận",
      "error": "Request timeout",
      "sentAt": "2025-01-20T10:30:45.123Z"
    }
  }
}
```

### 2. GET /api/zalo-webhook/test

**Mô tả:** Test endpoint để kiểm tra webhook có hoạt động không

**URL:** `http://localhost:3000/api/zalo-webhook/test`

**Method:** GET

**Response:**
```json
{
  "success": true,
  "message": "Zalo webhook endpoint is working",
  "data": {
    "endpoint": "/api/zalo-webhook",
    "method": "POST",
    "expectedFormat": {
      "type": 0,
      "data": {
        "actionId": "string",
        "msgId": "string",
        // ... other fields
      },
      "threadId": "string",
      "isSelf": "boolean"
    },
    "autoReplyMessage": "Đã Nhận",
    "webhookUrl": "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982",
    "timestamp": "2025-01-20T10:30:45.123Z"
  }
}
```

### 3. POST /api/zalo-webhook/test-send

**Mô tả:** Test gửi webhook thủ công để kiểm tra kết nối

**URL:** `http://localhost:3000/api/zalo-webhook/test-send`

**Method:** POST

**Content-Type:** application/json

**Request Body:**
```json
{
  "message": "Test message from API"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Test webhook sent successfully",
  "data": {
    "sentPayload": {
      "message": "Test message from API",
      "timestamp": "2025-01-20T10:30:45.123Z",
      "originalMessage": {
        "from": "Test User",
        "userId": "test-user-id",
        "content": "This is a test message",
        // ... other test data
      }
    },
    "webhookResponse": {
      "status": 200,
      "statusText": "OK",
      "data": {
        // Response từ webhook Mooly
      }
    }
  }
}
```

## Cách sử dụng

### 1. Khởi động server

```bash
# Development mode
yarn dev

# Production mode
yarn build
yarn start
```

Server sẽ chạy trên port 3000 (mặc định).

### 2. Test API với curl

**Test endpoint cơ bản:**
```bash
curl -X GET http://localhost:3000/api/zalo-webhook/test
```

**Test gửi webhook thủ công:**
```bash
curl -X POST http://localhost:3000/api/zalo-webhook/test-send \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello from test"}'
```

**Gửi tin nhắn Zalo mẫu:**
```bash
curl -X POST http://localhost:3000/api/zalo-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "type": 0,
    "data": {
      "actionId": "10800571650848",
      "msgId": "6824012833145",
      "cliMsgId": "1753014376324",
      "msgType": "webchat",
      "uidFrom": "4771680556510128931",
      "idTo": "1775737296024628671",
      "dName": "Test User",
      "ts": "1753014374857",
      "status": 1,
      "content": "Hello from test",
      "notify": "1",
      "ttl": 0,
      "userId": "0",
      "uin": "0",
      "topOut": "0",
      "topOutTimeOut": "0",
      "topOutImprTimeOut": "0",
      "propertyExt": {
        "color": 0,
        "size": 0,
        "type": 0,
        "subType": 0,
        "ext": "{\"shouldParseLinkOrContact\": 0}"
      },
      "paramsExt": {
        "countUnread": 1,
        "containType": 0,
        "platformType": 2
      },
      "cmd": 501,
      "st": 3,
      "at": 5,
      "realMsgId": "0"
    },
    "threadId": "4771680556510128931",
    "isSelf": false
  }'
```

## Webhook Target

API sẽ tự động gửi phản hồi đến:
- **URL:** `https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982`
- **Method:** POST
- **Content-Type:** application/json
- **Timeout:** 10 seconds

## Logging

Tất cả các hoạt động sẽ được ghi log chi tiết bao gồm:
- Tin nhắn nhận được từ Zalo
- Payload gửi đến webhook
- Response từ webhook
- Lỗi nếu có

## Error Handling

- API luôn trả về status 200 cho Zalo để tránh retry
- Lỗi webhook được ghi log nhưng không làm fail request
- Timeout được set 10 giây cho webhook calls
- Validation đầu vào để đảm bảo format đúng

## Monitoring

Kiểm tra logs để theo dõi:
- Số lượng tin nhắn nhận được
- Tỷ lệ thành công của webhook calls
- Thời gian phản hồi
- Lỗi và exceptions
