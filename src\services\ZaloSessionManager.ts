import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { zaloAuthService } from './ZaloAuthService';
import { zaloChatwootMappingService } from './ZaloChatwootMappingService';

export interface SessionHealth {
  accountId: string;
  tenantId: string;
  zaloUserId: string;
  isHealthy: boolean;
  lastCheck: Date;
  errorCount: number;
  lastError?: string;
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  errorSessions: number;
  pendingSessions: number;
  healthyConnections: number;
}

export class ZaloSessionManager {
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private sessionHealth: Map<string, SessionHealth> = new Map();
  private isRunning: boolean = false;

  constructor() {
    this.startHealthMonitoring();
  }

  /**
   * Khởi động health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    logger.info('Starting Zalo session health monitoring');

    // Health check every 5 minutes
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 5 * 60 * 1000);

    // Cleanup every hour
    this.cleanupInterval = setInterval(async () => {
      await this.performCleanup();
    }, 60 * 60 * 1000);

    // Initial health check
    setTimeout(() => this.performHealthCheck(), 10000); // 10 seconds after start
  }

  /**
   * Dừng health monitoring
   */
  stopHealthMonitoring(): void {
    if (!this.isRunning) return;

    this.isRunning = false;
    logger.info('Stopping Zalo session health monitoring');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Thực hiện health check cho tất cả active sessions
   */
  private async performHealthCheck(): Promise<void> {
    try {
      logger.info('Performing Zalo sessions health check');

      // Lấy tất cả active accounts
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('id, tenant_id, zalo_user_id, status, last_activity_at')
        .eq('status', 'active');

      if (error) {
        logger.error('Failed to fetch active accounts for health check', { error: error.message });
        return;
      }

      const healthPromises = (accounts || []).map(account => 
        this.checkAccountHealth(account.id, account.tenant_id, account.zalo_user_id)
      );

      await Promise.allSettled(healthPromises);

      // Log health summary
      const stats = this.getSessionStats();
      logger.info('Health check completed', stats);

    } catch (error: any) {
      logger.error('Health check failed', { error: error.message });
    }
  }

  /**
   * Kiểm tra health của một account
   */
  private async checkAccountHealth(accountId: string, tenantId: string, zaloUserId: string): Promise<void> {
    try {
      const zaloInstance = zaloAuthService.getZaloInstance(accountId);
      let isHealthy = false;
      let errorMessage: string | undefined;

      if (zaloInstance) {
        try {
          // Test connection bằng cách fetch account info
          const accountInfo = await zaloInstance.fetchAccountInfo();
          isHealthy = !!(accountInfo && accountInfo.uid);
          
          if (isHealthy) {
            // Update last activity
            await supabaseAdmin
              .from('zalo_accounts')
              .update({ last_activity_at: new Date().toISOString() })
              .eq('id', accountId);
          }
        } catch (error: any) {
          isHealthy = false;
          errorMessage = error.message;
          logger.warn('Account health check failed', {
            accountId,
            zaloUserId,
            error: error.message
          });
        }
      } else {
        isHealthy = false;
        errorMessage = 'No Zalo instance found';
      }

      // Update session health tracking
      const currentHealth = this.sessionHealth.get(accountId);
      const errorCount = isHealthy ? 0 : (currentHealth?.errorCount || 0) + 1;

      this.sessionHealth.set(accountId, {
        accountId,
        tenantId,
        zaloUserId,
        isHealthy,
        lastCheck: new Date(),
        errorCount,
        lastError: errorMessage
      });

      // Nếu có nhiều lỗi liên tiếp, thử reconnect
      if (errorCount >= 3) {
        logger.warn('Account has multiple consecutive errors, attempting reconnect', {
          accountId,
          zaloUserId,
          errorCount
        });
        
        await this.attemptReconnect(accountId, tenantId);
      }

    } catch (error: any) {
      logger.error('Failed to check account health', {
        accountId,
        zaloUserId,
        error: error.message
      });
    }
  }

  /**
   * Thử reconnect account
   */
  private async attemptReconnect(accountId: string, tenantId: string): Promise<void> {
    try {
      logger.info('Attempting to reconnect account', { accountId, tenantId });

      const success = await zaloAuthService.refreshSession(accountId, tenantId);
      
      if (success) {
        logger.info('Account reconnected successfully', { accountId });
        
        // Reset error count
        const currentHealth = this.sessionHealth.get(accountId);
        if (currentHealth) {
          this.sessionHealth.set(accountId, {
            ...currentHealth,
            isHealthy: true,
            errorCount: 0,
            lastError: undefined,
            lastCheck: new Date()
          });
        }
      } else {
        logger.error('Failed to reconnect account', { accountId });
        
        // Update account status to error
        await supabaseAdmin
          .from('zalo_accounts')
          .update({
            status: 'error',
            error_data: {
              error_message: 'Failed to reconnect after multiple health check failures',
              error_code: 'RECONNECT_FAILED',
              retry_count: 0,
              next_retry_at: new Date(Date.now() + 30 * 60 * 1000).toISOString() // Retry in 30 minutes
            }
          })
          .eq('id', accountId);
      }

    } catch (error: any) {
      logger.error('Reconnect attempt failed', {
        accountId,
        tenantId,
        error: error.message
      });
    }
  }

  /**
   * Thực hiện cleanup
   */
  private async performCleanup(): Promise<void> {
    try {
      logger.info('Performing cleanup');

      // Cleanup expired data
      await zaloAuthService.cleanupExpiredData();

      // Cleanup old health records
      const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      for (const [accountId, health] of this.sessionHealth.entries()) {
        if (health.lastCheck < cutoffTime) {
          this.sessionHealth.delete(accountId);
        }
      }

      // Retry failed accounts
      await this.retryFailedAccounts();

      logger.info('Cleanup completed');

    } catch (error: any) {
      logger.error('Cleanup failed', { error: error.message });
    }
  }

  /**
   * Retry các accounts bị lỗi
   */
  private async retryFailedAccounts(): Promise<void> {
    try {
      const { data: retryAccounts, error } = await supabaseAdmin.rpc('get_zalo_accounts_for_retry');

      if (error) {
        logger.error('Failed to get accounts for retry', { error: error.message });
        return;
      }

      for (const account of retryAccounts || []) {
        try {
          logger.info('Retrying failed account', {
            accountId: account.id,
            tenantId: account.tenant_id,
            retryCount: account.retry_count
          });

          const success = await zaloAuthService.refreshSession(account.id, account.tenant_id);

          if (success) {
            logger.info('Account retry successful', { accountId: account.id });

            // Clear error data
            await supabaseAdmin
              .from('zalo_accounts')
              .update({
                status: 'active',
                error_data: {}
              })
              .eq('id', account.id);
          } else {
            // Increment retry count
            const newRetryCount = (account.retry_count || 0) + 1;
            const nextRetryDelay = Math.min(30 * Math.pow(2, newRetryCount), 24 * 60); // Exponential backoff, max 24 hours

            await supabaseAdmin
              .from('zalo_accounts')
              .update({
                error_data: {
                  ...account.error_data,
                  retry_count: newRetryCount,
                  next_retry_at: new Date(Date.now() + nextRetryDelay * 60 * 1000).toISOString(),
                  last_retry_at: new Date().toISOString()
                }
              })
              .eq('id', account.id);

            logger.warn('Account retry failed, scheduled next retry', {
              accountId: account.id,
              retryCount: newRetryCount,
              nextRetryIn: `${nextRetryDelay} minutes`
            });
          }

        } catch (error: any) {
          logger.error('Failed to retry account', {
            accountId: account.id,
            error: error.message
          });
        }
      }

    } catch (error: any) {
      logger.error('Failed to retry failed accounts', { error: error.message });
    }
  }

  /**
   * Lấy thống kê sessions
   */
  getSessionStats(): SessionStats {
    const healthValues = Array.from(this.sessionHealth.values());

    return {
      totalSessions: this.sessionHealth.size,
      activeSessions: healthValues.filter(h => h.isHealthy).length,
      errorSessions: healthValues.filter(h => !h.isHealthy && h.errorCount > 0).length,
      pendingSessions: healthValues.filter(h => !h.isHealthy && h.errorCount === 0).length,
      healthyConnections: healthValues.filter(h => h.isHealthy && h.errorCount === 0).length
    };
  }

  /**
   * Lấy health status của một account
   */
  getAccountHealth(accountId: string): SessionHealth | null {
    return this.sessionHealth.get(accountId) || null;
  }

  /**
   * Lấy tất cả health status
   */
  getAllHealthStatus(): SessionHealth[] {
    return Array.from(this.sessionHealth.values());
  }

  /**
   * Force health check cho một account
   */
  async forceHealthCheck(accountId: string, tenantId: string, zaloUserId: string): Promise<SessionHealth> {
    await this.checkAccountHealth(accountId, tenantId, zaloUserId);
    return this.getAccountHealth(accountId) || {
      accountId,
      tenantId,
      zaloUserId,
      isHealthy: false,
      lastCheck: new Date(),
      errorCount: 1,
      lastError: 'Health check failed'
    };
  }

  /**
   * Khởi tạo lại tất cả sessions từ database
   */
  async reinitializeAllSessions(): Promise<void> {
    try {
      logger.info('Reinitializing all Zalo sessions');

      // Clear current health tracking
      this.sessionHealth.clear();

      // Get all active accounts
      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active');

      if (error) {
        logger.error('Failed to fetch accounts for reinitialization', { error: error.message });
        return;
      }

      let successCount = 0;
      let failCount = 0;

      for (const account of accounts || []) {
        try {
          const success = await zaloAuthService.refreshSession(account.id, account.tenant_id);
          if (success) {
            successCount++;
          } else {
            failCount++;
          }
        } catch (error: any) {
          failCount++;
          logger.error('Failed to reinitialize account', {
            accountId: account.id,
            error: error.message
          });
        }
      }

      logger.info('Session reinitialization completed', {
        total: accounts?.length || 0,
        success: successCount,
        failed: failCount
      });

    } catch (error: any) {
      logger.error('Failed to reinitialize sessions', { error: error.message });
    }
  }

  /**
   * Xử lý message từ Zalo webhook
   */
  async handleZaloMessage(accountId: string, messageData: any): Promise<void> {
    try {
      // Update account activity
      const health = this.sessionHealth.get(accountId);
      if (health) {
        this.sessionHealth.set(accountId, {
          ...health,
          isHealthy: true,
          lastCheck: new Date(),
          errorCount: 0,
          lastError: undefined
        });
      }

      // Update database activity
      await supabaseAdmin
        .from('zalo_accounts')
        .update({ last_activity_at: new Date().toISOString() })
        .eq('id', accountId);

      logger.debug('Zalo message handled, account activity updated', { accountId });

    } catch (error: any) {
      logger.error('Failed to handle Zalo message', {
        accountId,
        error: error.message
      });
    }
  }
}

// Export singleton instance
export const zaloSessionManager = new ZaloSessionManager();
