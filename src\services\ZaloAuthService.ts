import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';
import { Zalo } from 'zca-js';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';

// Interfaces
export interface ZaloAccount {
  id?: string;
  tenant_id: string;
  zalo_user_id: string;
  zalo_display_name?: string;
  zalo_phone?: string;
  zalo_avatar_url?: string;
  auth_data: {
    cookies: any;
    imei: string;
    user_agent: string;
    session_token?: string;
  };
  status: 'active' | 'inactive' | 'expired' | 'error' | 'pending' | 'qr_pending';
  qr_data?: {
    qr_code_path?: string;
    qr_status?: 'pending' | 'scanned' | 'confirmed' | 'expired' | 'failed';
    qr_expires_at?: string;
    qr_session_id?: string;
  };
  chatwoot_inbox_id?: number;
  chatwoot_source_id?: string;
  chatwoot_channel_status?: 'active' | 'inactive' | 'error';
  error_data?: {
    error_message?: string;
    error_code?: string;
    retry_count?: number;
    next_retry_at?: string;
  };
  login_method: 'qr' | 'cookie';
  settings?: any;
  metadata?: any;
  last_login_at?: string;
  last_activity_at?: string;
  expires_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QRLoginRequest {
  tenant_id: string;
  user_agent?: string;
  expires_in_minutes?: number;
}

export interface QRLoginResponse {
  account_id: string;
  qr_code_path: string;
  qr_session_id: string;
  expires_at: string;
  status: string;
}

export interface CookieLoginRequest {
  tenant_id: string;
  cookies: any;
  imei: string;
  user_agent: string;
  zalo_user_id?: string;
  zalo_display_name?: string;
}

export interface AccountStatusResponse {
  account_id: string;
  zalo_user_id: string;
  status: string;
  is_connected: boolean;
  last_activity_at?: string;
  expires_at?: string;
  chatwoot_status?: string;
  error_message?: string;
}

export class ZaloAuthService {
  private zaloInstances: Map<string, any> = new Map(); // accountId -> Zalo instance
  private qrSessions: Map<string, any> = new Map(); // sessionId -> session data

  constructor() {
    this.initializeFromDatabase();
  }

  /**
   * Khởi tạo các Zalo instances từ database khi server start
   */
  private async initializeFromDatabase(): Promise<void> {
    try {
      logger.info('Initializing Zalo instances from database');

      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('status', 'active');

      if (error) {
        logger.error('Failed to load active Zalo accounts', { error: error.message });
        return;
      }

      for (const account of accounts || []) {
        try {
          await this.initializeZaloInstance(account);
        } catch (error: any) {
          logger.error('Failed to initialize Zalo instance', {
            accountId: account.id,
            error: error.message
          });
        }
      }

      logger.info('Zalo instances initialized', { count: this.zaloInstances.size });
    } catch (error: any) {
      logger.error('Failed to initialize from database', { error: error.message });
    }
  }

  /**
   * Khởi tạo Zalo instance từ account data
   */
  private async initializeZaloInstance(account: ZaloAccount): Promise<any> {
    try {
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: false
      });

      const api = await zalo.login({
        cookie: account.auth_data.cookies,
        imei: account.auth_data.imei,
        userAgent: account.auth_data.user_agent
      });

      // Setup listener for messages
      api.listener.start();
      
      // Store instance
      this.zaloInstances.set(account.id!, api);

      logger.info('Zalo instance initialized', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id
      });

      return api;
    } catch (error: any) {
      logger.error('Failed to initialize Zalo instance', {
        accountId: account.id,
        error: error.message
      });
      
      // Update account status to error
      await this.updateAccountStatus(account.id!, account.tenant_id, 'error', {
        error_message: error.message,
        error_code: 'INIT_FAILED'
      });
      
      throw error;
    }
  }

  /**
   * Tạo QR code để đăng nhập
   */
  async generateQRLogin(request: QRLoginRequest): Promise<QRLoginResponse> {
    try {
      logger.info('Generating QR login', { tenantId: request.tenant_id });

      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: request.tenant_id,
        is_local: true
      });

      const qrSessionId = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + (request.expires_in_minutes || 10) * 60 * 1000);
      const qrPath = path.join(process.cwd(), 'qr_codes', `${qrSessionId}.png`);

      // Ensure qr_codes directory exists
      const qrDir = path.dirname(qrPath);
      if (!fs.existsSync(qrDir)) {
        fs.mkdirSync(qrDir, { recursive: true });
      }

      // Create Zalo instance for QR login
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: false
      });

      // Create temporary account record
      const { data: account, error } = await supabaseAdmin
        .from('zalo_accounts')
        .insert({
          tenant_id: request.tenant_id,
          zalo_user_id: 'pending_' + qrSessionId,
          status: 'qr_pending',
          auth_data: {
            user_agent: request.user_agent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            imei: '',
            cookies: {}
          },
          qr_data: {
            qr_session_id: qrSessionId,
            qr_status: 'pending',
            qr_expires_at: expiresAt.toISOString(),
            qr_code_path: qrPath
          },
          login_method: 'qr'
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create QR session: ${error.message}`);
      }

      // Generate QR code
      const api = await zalo.loginQR({
        userAgent: request.user_agent,
        qrPath: qrPath
      }, (qrCodePath: string) => {
        logger.info('QR code generated', { qrCodePath, sessionId: qrSessionId });
      });

      // Store QR session
      this.qrSessions.set(qrSessionId, {
        accountId: account.id,
        tenantId: request.tenant_id,
        api: api,
        expiresAt: expiresAt
      });

      // Setup QR login completion handler
      this.setupQRLoginHandler(qrSessionId, account.id, request.tenant_id, api);

      logger.info('QR login session created', {
        accountId: account.id,
        sessionId: qrSessionId,
        expiresAt: expiresAt.toISOString()
      });

      return {
        account_id: account.id,
        qr_code_path: qrPath,
        qr_session_id: qrSessionId,
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      };

    } catch (error: any) {
      logger.error('Failed to generate QR login', {
        tenantId: request.tenant_id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Setup QR login completion handler
   */
  private setupQRLoginHandler(sessionId: string, accountId: string, tenantId: string, api: any): void {
    // Check for login completion periodically
    const checkInterval = setInterval(async () => {
      try {
        const session = this.qrSessions.get(sessionId);
        if (!session || new Date() > session.expiresAt) {
          clearInterval(checkInterval);
          this.qrSessions.delete(sessionId);
          
          // Update account status to expired
          await this.updateQRStatus(accountId, tenantId, 'expired');
          return;
        }

        // Check if login is successful by trying to get user info
        try {
          const userInfo = await api.fetchAccountInfo();
          if (userInfo && userInfo.uid) {
            clearInterval(checkInterval);
            
            // Login successful - update account
            await this.completeQRLogin(sessionId, accountId, tenantId, api, userInfo);
          }
        } catch (checkError) {
          // Login not yet complete, continue checking
        }
      } catch (error: any) {
        logger.error('Error in QR login check', { sessionId, error: error.message });
      }
    }, 2000); // Check every 2 seconds

    // Cleanup after expiry
    setTimeout(() => {
      clearInterval(checkInterval);
      this.qrSessions.delete(sessionId);
    }, 15 * 60 * 1000); // 15 minutes max
  }

  /**
   * Complete QR login process
   */
  private async completeQRLogin(sessionId: string, accountId: string, tenantId: string, api: any, userInfo: any): Promise<void> {
    try {
      logger.info('Completing QR login', { sessionId, accountId, userId: userInfo.uid });

      // Get cookies and session data
      const cookies = await api.getCookie();
      const imei = userInfo.imei || crypto.randomUUID();

      // Update account with auth data
      await supabaseAdmin
        .from('zalo_accounts')
        .update({
          zalo_user_id: userInfo.uid,
          zalo_display_name: userInfo.displayName || userInfo.name,
          zalo_phone: userInfo.phone,
          zalo_avatar_url: userInfo.avatar,
          auth_data: {
            cookies: cookies,
            imei: imei,
            user_agent: userInfo.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            session_token: crypto.randomUUID()
          },
          status: 'active',
          last_login_at: new Date().toISOString(),
          last_activity_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
          qr_data: {
            ...((await this.getAccount(accountId, tenantId))?.qr_data || {}),
            qr_status: 'confirmed'
          }
        })
        .eq('id', accountId)
        .eq('tenant_id', tenantId);

      // Store Zalo instance
      this.zaloInstances.set(accountId, api);

      // Cleanup QR session
      this.qrSessions.delete(sessionId);

      logger.info('QR login completed successfully', {
        accountId,
        zaloUserId: userInfo.uid,
        displayName: userInfo.displayName
      });

    } catch (error: any) {
      logger.error('Failed to complete QR login', {
        sessionId,
        accountId,
        error: error.message
      });

      await this.updateAccountStatus(accountId, tenantId, 'error', {
        error_message: error.message,
        error_code: 'QR_COMPLETION_FAILED'
      });
    }
  }

  /**
   * Đăng nhập bằng cookie
   */
  async loginWithCookie(request: CookieLoginRequest): Promise<ZaloAccount> {
    try {
      logger.info('Login with cookie', { tenantId: request.tenant_id });

      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: request.tenant_id,
        is_local: true
      });

      // Test login with provided credentials
      const zalo = new Zalo({
        selfListen: false,
        checkUpdate: false,
        logging: false
      });

      const api = await zalo.login({
        cookie: request.cookies,
        imei: request.imei,
        userAgent: request.user_agent
      });

      // Get user info to validate login
      const userInfo = await api.fetchAccountInfo();
      if (!userInfo || !userInfo.uid) {
        throw new Error('Invalid credentials - unable to fetch account info');
      }

      const zaloUserId = request.zalo_user_id || userInfo.uid;

      // Check if account already exists
      const { data: existingAccount } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('tenant_id', request.tenant_id)
        .eq('zalo_user_id', zaloUserId)
        .single();

      let account: ZaloAccount;

      if (existingAccount) {
        // Update existing account
        const { data: updatedAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .update({
            auth_data: {
              cookies: request.cookies,
              imei: request.imei,
              user_agent: request.user_agent,
              session_token: crypto.randomUUID()
            },
            status: 'active',
            zalo_display_name: request.zalo_display_name || userInfo.displayName || userInfo.name,
            zalo_phone: userInfo.phone,
            zalo_avatar_url: userInfo.avatar,
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            error_data: {} // Clear any previous errors
          })
          .eq('id', existingAccount.id)
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to update account: ${error.message}`);
        }

        account = updatedAccount;
      } else {
        // Create new account
        const { data: newAccount, error } = await supabaseAdmin
          .from('zalo_accounts')
          .insert({
            tenant_id: request.tenant_id,
            zalo_user_id: zaloUserId,
            zalo_display_name: request.zalo_display_name || userInfo.displayName || userInfo.name,
            zalo_phone: userInfo.phone,
            zalo_avatar_url: userInfo.avatar,
            auth_data: {
              cookies: request.cookies,
              imei: request.imei,
              user_agent: request.user_agent,
              session_token: crypto.randomUUID()
            },
            status: 'active',
            login_method: 'cookie',
            last_login_at: new Date().toISOString(),
            last_activity_at: new Date().toISOString(),
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to create account: ${error.message}`);
        }

        account = newAccount;
      }

      // Store Zalo instance
      api.listener.start();
      this.zaloInstances.set(account.id!, api);

      logger.info('Cookie login successful', {
        accountId: account.id,
        zaloUserId: account.zalo_user_id,
        displayName: account.zalo_display_name
      });

      return account;

    } catch (error: any) {
      logger.error('Cookie login failed', {
        tenantId: request.tenant_id,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Kiểm tra trạng thái QR code
   */
  async checkQRStatus(accountId: string, tenantId: string): Promise<{ status: string; account?: ZaloAccount }> {
    try {
      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });

      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        return { status: 'not_found' };
      }

      const qrStatus = account.qr_data?.qr_status || 'unknown';

      // Check if QR has expired
      if (account.qr_data?.qr_expires_at) {
        const expiresAt = new Date(account.qr_data.qr_expires_at);
        if (new Date() > expiresAt && qrStatus === 'pending') {
          await this.updateQRStatus(accountId, tenantId, 'expired');
          return { status: 'expired', account };
        }
      }

      return { status: qrStatus, account };

    } catch (error: any) {
      logger.error('Failed to check QR status', {
        accountId,
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy trạng thái account
   */
  async getAccountStatus(accountId: string, tenantId: string): Promise<AccountStatusResponse> {
    try {
      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });

      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        throw new Error('Account not found');
      }

      const zaloInstance = this.zaloInstances.get(accountId);
      let isConnected = false;

      // Test connection if instance exists
      if (zaloInstance && account.status === 'active') {
        try {
          await zaloInstance.fetchAccountInfo();
          isConnected = true;

          // Update last activity
          await this.updateAccountActivity(accountId, tenantId);
        } catch (error) {
          isConnected = false;
          logger.warn('Zalo instance connection test failed', {
            accountId,
            error: (error as Error).message
          });
        }
      }

      return {
        account_id: accountId,
        zalo_user_id: account.zalo_user_id,
        status: account.status,
        is_connected: isConnected,
        last_activity_at: account.last_activity_at,
        expires_at: account.expires_at,
        chatwoot_status: account.chatwoot_channel_status,
        error_message: account.error_data?.error_message
      };

    } catch (error: any) {
      logger.error('Failed to get account status', {
        accountId,
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Lấy danh sách accounts của tenant
   */
  async getAccountsByTenant(tenantId: string): Promise<ZaloAccount[]> {
    try {
      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });

      const { data: accounts, error } = await supabaseAdmin
        .from('zalo_accounts')
        .select('*')
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Failed to get accounts: ${error.message}`);
      }

      return accounts || [];

    } catch (error: any) {
      logger.error('Failed to get accounts by tenant', {
        tenantId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Refresh session cho account
   */
  async refreshSession(accountId: string, tenantId: string): Promise<boolean> {
    try {
      logger.info('Refreshing session', { accountId, tenantId });

      const account = await this.getAccount(accountId, tenantId);
      if (!account) {
        throw new Error('Account not found');
      }

      // Remove old instance
      this.zaloInstances.delete(accountId);

      // Try to reinitialize
      await this.initializeZaloInstance(account);

      // Update last activity
      await this.updateAccountActivity(accountId, tenantId);

      logger.info('Session refreshed successfully', { accountId });
      return true;

    } catch (error: any) {
      logger.error('Failed to refresh session', {
        accountId,
        tenantId,
        error: error.message
      });

      await this.updateAccountStatus(accountId, tenantId, 'error', {
        error_message: error.message,
        error_code: 'REFRESH_FAILED'
      });

      return false;
    }
  }

  /**
   * Xóa account
   */
  async removeAccount(accountId: string, tenantId: string): Promise<boolean> {
    try {
      logger.info('Removing account', { accountId, tenantId });

      // Set tenant context
      await supabaseAdmin.rpc('set_config', {
        setting_name: 'app.current_tenant_id',
        setting_value: tenantId,
        is_local: true
      });

      // Remove Zalo instance
      const zaloInstance = this.zaloInstances.get(accountId);
      if (zaloInstance) {
        try {
          zaloInstance.listener.stop();
        } catch (error) {
          // Ignore listener stop errors
        }
        this.zaloInstances.delete(accountId);
      }

      // Delete from database
      const { error } = await supabaseAdmin
        .from('zalo_accounts')
        .delete()
        .eq('id', accountId)
        .eq('tenant_id', tenantId);

      if (error) {
        throw new Error(`Failed to delete account: ${error.message}`);
      }

      logger.info('Account removed successfully', { accountId });
      return true;

    } catch (error: any) {
      logger.error('Failed to remove account', {
        accountId,
        tenantId,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Lấy Zalo instance cho account
   */
  getZaloInstance(accountId: string): any {
    return this.zaloInstances.get(accountId);
  }

  /**
   * Helper methods
   */
  private async getAccount(accountId: string, tenantId: string): Promise<ZaloAccount | null> {
    const { data, error } = await supabaseAdmin
      .from('zalo_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null;
      }
      throw error;
    }

    return data;
  }

  private async updateAccountStatus(accountId: string, tenantId: string, status: string, errorData?: any): Promise<void> {
    const updateData: any = { status };

    if (errorData) {
      updateData.error_data = errorData;
    }

    await supabaseAdmin
      .from('zalo_accounts')
      .update(updateData)
      .eq('id', accountId)
      .eq('tenant_id', tenantId);
  }

  private async updateQRStatus(accountId: string, tenantId: string, qrStatus: string): Promise<void> {
    const { data: account } = await supabaseAdmin
      .from('zalo_accounts')
      .select('qr_data')
      .eq('id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (account) {
      const updatedQRData = {
        ...account.qr_data,
        qr_status: qrStatus,
        updated_at: new Date().toISOString()
      };

      await supabaseAdmin
        .from('zalo_accounts')
        .update({ qr_data: updatedQRData })
        .eq('id', accountId)
        .eq('tenant_id', tenantId);
    }
  }

  private async updateAccountActivity(accountId: string, tenantId: string): Promise<void> {
    await supabaseAdmin
      .from('zalo_accounts')
      .update({
        last_activity_at: new Date().toISOString(),
        status: 'active'
      })
      .eq('id', accountId)
      .eq('tenant_id', tenantId);
  }

  /**
   * Cleanup expired data periodically
   */
  async cleanupExpiredData(): Promise<void> {
    try {
      const { data, error } = await supabaseAdmin.rpc('cleanup_expired_zalo_data');

      if (error) {
        logger.error('Failed to cleanup expired data', { error: error.message });
        return;
      }

      if (data && data.length > 0) {
        const result = data[0];
        logger.info('Cleanup completed', {
          expiredQR: result.expired_qr_count,
          expiredSessions: result.expired_session_count,
          totalCleaned: result.total_cleaned
        });
      }
    } catch (error: any) {
      logger.error('Cleanup process failed', { error: error.message });
    }
  }
}

// Export singleton instance
export const zaloAuthService = new ZaloAuthService();
