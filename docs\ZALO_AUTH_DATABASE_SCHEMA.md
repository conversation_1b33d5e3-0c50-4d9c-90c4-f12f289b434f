# Zalo Authentication Database Schema

Hệ thống database cho Zalo authentication multi-tenant với Row Level Security (RLS).

## Tổng quan

Schema này hỗ trợ:
- **Multi-tenant**: Mỗi tenant có thể có nhiều Zalo accounts
- **Session Management**: <PERSON> dõi trạng thái đăng nhập và session
- **QR Code Tracking**: Quản lý QR login flow
- **Chatwoot Integration**: <PERSON>ên kết với Chatwoot inboxes
- **Security**: RLS policies cho tenant isolation

## Bảng `zalo_accounts`

### Schema

```sql
CREATE TABLE public.zalo_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
    
    -- Zalo account information
    zalo_user_id VARCHAR(255) NOT NULL,
    zalo_display_name VARCHAR(255),
    zalo_phone VARCHAR(20),
    zalo_avatar_url TEXT,
    
    -- Authentication data (encrypted)
    cookies JSONB NOT NULL,
    imei VARCHAR(255) NOT NULL,
    user_agent TEXT NOT NULL,
    
    -- Session status
    status VARCHAR(50) DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'expired', 'error', 'pending')),
    last_login_at TIMESTAMPTZ,
    last_activity_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- QR login tracking
    qr_code_path VARCHAR(500),
    qr_status VARCHAR(50) DEFAULT 'pending' CHECK (qr_status IN ('pending', 'scanned', 'confirmed', 'expired', 'failed')),
    qr_expires_at TIMESTAMPTZ,
    qr_session_id VARCHAR(255),
    
    -- Chatwoot integration
    chatwoot_inbox_id INTEGER,
    chatwoot_source_id VARCHAR(255),
    chatwoot_channel_status VARCHAR(50) DEFAULT 'inactive' CHECK (chatwoot_channel_status IN ('active', 'inactive', 'error')),
    
    -- Error handling and retry logic
    login_method VARCHAR(20) DEFAULT 'qr' CHECK (login_method IN ('qr', 'cookie')),
    error_message TEXT,
    error_code VARCHAR(50),
    retry_count INTEGER DEFAULT 0,
    max_retry_count INTEGER DEFAULT 3,
    next_retry_at TIMESTAMPTZ,
    
    -- Metadata and configuration
    metadata JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    
    -- Audit timestamps
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    -- Constraints
    CONSTRAINT unique_tenant_zalo_user UNIQUE(tenant_id, zalo_user_id),
    CONSTRAINT unique_tenant_chatwoot_inbox UNIQUE(tenant_id, chatwoot_inbox_id),
    CONSTRAINT valid_expires_at CHECK (expires_at > created_at),
    CONSTRAINT valid_qr_expires CHECK (qr_expires_at IS NULL OR qr_expires_at > created_at)
);
```

### Indexes

```sql
-- Primary indexes for performance
CREATE INDEX idx_zalo_accounts_tenant_id ON zalo_accounts(tenant_id);
CREATE INDEX idx_zalo_accounts_zalo_user_id ON zalo_accounts(zalo_user_id);
CREATE INDEX idx_zalo_accounts_status ON zalo_accounts(status) WHERE status = 'active';
CREATE INDEX idx_zalo_accounts_qr_status ON zalo_accounts(qr_status, qr_expires_at) WHERE qr_status IN ('pending', 'scanned');
CREATE INDEX idx_zalo_accounts_chatwoot_inbox ON zalo_accounts(chatwoot_inbox_id) WHERE chatwoot_inbox_id IS NOT NULL;

-- Composite indexes for common queries
CREATE INDEX idx_zalo_accounts_tenant_status ON zalo_accounts(tenant_id, status);
CREATE INDEX idx_zalo_accounts_last_activity ON zalo_accounts(last_activity_at DESC) WHERE status = 'active';
CREATE INDEX idx_zalo_accounts_expires_at ON zalo_accounts(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_zalo_accounts_retry ON zalo_accounts(next_retry_at) WHERE next_retry_at IS NOT NULL AND retry_count < max_retry_count;
```

### Triggers

```sql
-- Auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_zalo_accounts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_zalo_accounts_updated_at
    BEFORE UPDATE ON zalo_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_zalo_accounts_updated_at();

-- Auto-cleanup expired QR codes
CREATE OR REPLACE FUNCTION cleanup_expired_qr_codes()
RETURNS TRIGGER AS $$
BEGIN
    -- Set QR status to expired if past expiry time
    IF NEW.qr_expires_at IS NOT NULL AND NEW.qr_expires_at < now() AND NEW.qr_status = 'pending' THEN
        NEW.qr_status = 'expired';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_cleanup_expired_qr
    BEFORE UPDATE ON zalo_accounts
    FOR EACH ROW
    EXECUTE FUNCTION cleanup_expired_qr_codes();
```

## Row Level Security (RLS)

### Enable RLS

```sql
ALTER TABLE zalo_accounts ENABLE ROW LEVEL SECURITY;
```

### RLS Policies

```sql
-- Policy for SELECT: Users can only see their tenant's accounts
CREATE POLICY "tenant_zalo_accounts_select" ON zalo_accounts
    FOR SELECT
    USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- Policy for INSERT: Users can only create accounts for their tenant
CREATE POLICY "tenant_zalo_accounts_insert" ON zalo_accounts
    FOR INSERT
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- Policy for UPDATE: Users can only update their tenant's accounts
CREATE POLICY "tenant_zalo_accounts_update" ON zalo_accounts
    FOR UPDATE
    USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid)
    WITH CHECK (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- Policy for DELETE: Users can only delete their tenant's accounts
CREATE POLICY "tenant_zalo_accounts_delete" ON zalo_accounts
    FOR DELETE
    USING (tenant_id = current_setting('app.current_tenant_id', true)::uuid);

-- Service role bypass policy (for system operations)
CREATE POLICY "service_role_zalo_accounts_all" ON zalo_accounts
    FOR ALL
    TO service_role
    USING (true)
    WITH CHECK (true);
```

## Bảng `zalo_sessions` (Optional - for detailed session tracking)

```sql
CREATE TABLE public.zalo_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    zalo_account_id UUID NOT NULL REFERENCES zalo_accounts(id) ON DELETE CASCADE,
    
    -- Session information
    session_token VARCHAR(255) NOT NULL,
    session_data JSONB DEFAULT '{}',
    
    -- Status and timing
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'terminated')),
    started_at TIMESTAMPTZ DEFAULT now(),
    expires_at TIMESTAMPTZ NOT NULL,
    last_used_at TIMESTAMPTZ DEFAULT now(),
    
    -- Connection info
    ip_address INET,
    user_agent TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    
    CONSTRAINT valid_session_expires CHECK (expires_at > started_at)
);

-- Indexes for zalo_sessions
CREATE INDEX idx_zalo_sessions_account_id ON zalo_sessions(zalo_account_id);
CREATE INDEX idx_zalo_sessions_status ON zalo_sessions(status, expires_at);
CREATE INDEX idx_zalo_sessions_token ON zalo_sessions(session_token) WHERE status = 'active';
```

## Utility Functions

```sql
-- Function to get active Zalo accounts for a tenant
CREATE OR REPLACE FUNCTION get_active_zalo_accounts(p_tenant_id UUID)
RETURNS TABLE (
    id UUID,
    zalo_user_id VARCHAR(255),
    zalo_display_name VARCHAR(255),
    status VARCHAR(50),
    chatwoot_inbox_id INTEGER,
    last_activity_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        za.id,
        za.zalo_user_id,
        za.zalo_display_name,
        za.status,
        za.chatwoot_inbox_id,
        za.last_activity_at
    FROM zalo_accounts za
    WHERE za.tenant_id = p_tenant_id
      AND za.status = 'active'
    ORDER BY za.last_activity_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to cleanup expired sessions and QR codes
CREATE OR REPLACE FUNCTION cleanup_expired_zalo_data()
RETURNS INTEGER AS $$
DECLARE
    cleanup_count INTEGER := 0;
BEGIN
    -- Update expired QR codes
    UPDATE zalo_accounts 
    SET qr_status = 'expired'
    WHERE qr_status = 'pending' 
      AND qr_expires_at < now();
    
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    
    -- Update expired sessions
    UPDATE zalo_accounts 
    SET status = 'expired'
    WHERE status = 'active' 
      AND expires_at < now();
    
    GET DIAGNOSTICS cleanup_count = cleanup_count + ROW_COUNT;
    
    RETURN cleanup_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Migration Script

```sql
-- Migration to add zalo_accounts table
-- Run this after ensuring tenants table exists

BEGIN;

-- Create the main table
-- (Schema already defined above)

-- Insert initial data if needed
-- INSERT INTO zalo_accounts (tenant_id, zalo_user_id, ...) VALUES (...);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON zalo_accounts TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON zalo_sessions TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

COMMIT;
```

## Best Practices

1. **Encryption**: Sensitive data như cookies nên được encrypt trước khi lưu
2. **Session Management**: Định kỳ cleanup expired sessions
3. **Monitoring**: Track account status và connection health
4. **Backup**: Regular backup cho authentication data
5. **Audit**: Log tất cả authentication events
6. **Rate Limiting**: Implement rate limiting cho QR generation
7. **Security**: Validate tenant context trong mọi operations

## Troubleshooting

### Common Queries

```sql
-- Check account status for a tenant
SELECT id, zalo_user_id, status, last_activity_at 
FROM zalo_accounts 
WHERE tenant_id = 'your-tenant-id';

-- Find expired accounts
SELECT * FROM zalo_accounts 
WHERE status = 'active' AND expires_at < now();

-- Check QR code status
SELECT id, qr_status, qr_expires_at 
FROM zalo_accounts 
WHERE qr_status = 'pending';
```
