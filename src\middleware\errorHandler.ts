import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import '../types/api';

// Declare Node.js globals
declare const process: any;

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class AppError extends Error implements ApiError {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code?: string, details?: any) {
    super(message);
    this.statusCode = statusCode;
    this.code = code || 'INTERNAL_ERROR';
    this.details = details;
    this.name = 'AppError';

    // Maintains proper stack trace for where our error was thrown
    if ((Error as any).captureStackTrace) {
      (Error as any).captureStackTrace(this, this.constructor);
    }
  }
}

export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const statusCode = error.statusCode || 500;
  const message = error.message || 'Internal Server Error';
  const code = error.code || 'INTERNAL_ERROR';

  // Log error details
  logger.error('API Error:', {
    requestId: req.id,
    method: req.method,
    url: req.url,
    statusCode,
    code,
    message,
    stack: error.stack,
    details: error.details
  });

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV !== 'production';

  const errorResponse = {
    success: false,
    error: {
      code,
      message,
      ...(isDevelopment && {
        stack: error.stack,
        details: error.details
      })
    },
    requestId: req.id,
    timestamp: new Date().toISOString()
  };

  res.status(statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};