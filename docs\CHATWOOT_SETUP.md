# Hướng dẫn cấu hình Chatwoot API Channel

## Tổng quan

Tài liệu này hướng dẫn chi tiết cách cấu hình API Channel trong Chatwoot để tích hợp với <PERSON>alo Webhook, cho phép tự động tạo contact, conversation và message khi có tin nhắn từ Zalo.

## Yêu cầu

- Tài k<PERSON> Chatwoot (Cloud hoặc Self-hosted)
- Quyền Admin để tạo inbox và lấy API token
- Zalo Webhook đã được cấu hình

## Bước 1: Tạo API Channel Inbox trong Chatwoot

### 1.1 Truy cập Settings
1. Đăng nhập vào Chatwoot
2. Vào **Settings** → **Inboxes**
3. Nhấn **"Add Inbox"**

### 1.2 Chọn API Channel
1. Trong danh sách channels, chọn **"API"**
2. Điền thông tin:
   - **Channel Name**: `Zalo Integration` (hoặc tên bạn muốn)
   - **Callback URL**: URL webhook của bạn (ví dụ: `https://your-domain.com/api/zalo-webhook`)

### 1.3 Thêm Agents
1. Chọn các agents sẽ xử lý tin nhắn từ channel này
2. Nhấn **"Create API Channel"**

### 1.4 Lấy thông tin cần thiết
Sau khi tạo thành công, bạn sẽ có:
- **Inbox ID**: Số ID của inbox (ví dụ: 281)
- **Inbox Identifier**: Chuỗi định danh inbox

## Bước 2: Lấy API Access Token

### 2.1 Tạo API Token
1. Vào **Profile Settings** (avatar ở góc phải)
2. Chọn **"Access Token"**
3. Nhấn **"Create New Token"**
4. Copy token được tạo (chỉ hiển thị 1 lần)

### 2.2 Lấy Account ID
Account ID có thể lấy từ URL khi đang ở dashboard:
```
https://app.chatwoot.com/app/accounts/213/dashboard
                                    ^^^
                                Account ID
```

## Bước 3: Cấu hình Environment Variables

Thêm các biến môi trường sau vào file `.env`:

```env
# Chatwoot Configuration
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_API_ACCESS_TOKEN=your_api_access_token_here
CHATWOOT_ACCOUNT_ID=213
CHATWOOT_INBOX_ID=281
CHATWOOT_ENABLED=true
```

### Giải thích các biến:
- `CHATWOOT_BASE_URL`: URL của Chatwoot instance
- `CHATWOOT_API_ACCESS_TOKEN`: Token từ Profile Settings
- `CHATWOOT_ACCOUNT_ID`: ID của account (từ URL)
- `CHATWOOT_INBOX_ID`: ID của API inbox đã tạo
- `CHATWOOT_ENABLED`: Bật/tắt tích hợp (true/false)

## Bước 4: Kiểm tra cấu hình

### 4.1 Test connection
Gọi endpoint test để kiểm tra kết nối:
```bash
GET /api/zalo-webhook/chatwoot-test
```

### 4.2 Test tạo contact và conversation
Gửi tin nhắn test qua Zalo để kiểm tra:
```bash
POST /api/zalo-webhook/test-send
```

## Cấu trúc API Chatwoot

### API Endpoints sử dụng:

1. **Tạo Contact**:
   ```
   POST /api/v1/accounts/{account_id}/contacts
   ```

2. **Tạo Conversation**:
   ```
   POST /api/v1/accounts/{account_id}/conversations
   ```

3. **Tạo Message**:
   ```
   POST /api/v1/accounts/{account_id}/conversations/{conversation_id}/messages
   ```

### Flow xử lý tin nhắn:

1. **Nhận tin nhắn từ Zalo** → Webhook endpoint
2. **Tạo/Tìm Contact** → Sử dụng `uidFrom` làm identifier
3. **Tạo/Tìm Conversation** → Sử dụng `source_id` từ contact_inbox
4. **Tạo Message** → Tin nhắn incoming từ user
5. **Gửi webhook response** → Phản hồi tự động

## Troubleshooting

### Lỗi thường gặp:

1. **401 Unauthorized**:
   - Kiểm tra API Access Token
   - Đảm bảo token chưa hết hạn

2. **404 Not Found**:
   - Kiểm tra Account ID và Inbox ID
   - Đảm bảo URL base đúng

3. **422 Unprocessable Entity**:
   - Kiểm tra format dữ liệu gửi lên
   - Đảm bảo các field required đã có

4. **Contact duplicate**:
   - Sử dụng identifier để tránh tạo trùng
   - Implement logic tìm kiếm trước khi tạo

### Debug logs:
Kiểm tra logs để debug:
```bash
# Xem logs của ứng dụng
tail -f logs/app.log

# Tìm logs liên quan đến Chatwoot
grep "Chatwoot" logs/app.log
```

## Webhook Events từ Chatwoot

Khi có tin nhắn mới trong Chatwoot, sẽ gửi webhook đến Callback URL với format:

```json
{
  "event": "message_created",
  "id": 123,
  "content": "Nội dung tin nhắn",
  "message_type": "outgoing",
  "sender": {
    "id": 1,
    "name": "Agent Name",
    "type": "agent"
  },
  "conversation": {
    "id": 456,
    "status": "open"
  },
  "account": {
    "id": 213,
    "name": "Account Name"
  }
}
```

## Tính năng nâng cao

### 1. Custom Attributes
Có thể thêm thông tin bổ sung vào contact:
```json
{
  "additional_attributes": {
    "zalo_uid": "user_id_from_zalo",
    "source": "zalo_webhook",
    "phone": "+***********"
  }
}
```

### 2. Labels và Tags
Tự động gán labels cho conversation:
```json
{
  "labels": ["zalo", "webhook", "auto-created"]
}
```

### 3. Assignment Rules
Cấu hình rules để tự động assign conversation cho agents.

## Bảo mật

1. **HTTPS**: Luôn sử dụng HTTPS cho webhook URL
2. **Token Security**: Bảo mật API token, không commit vào code
3. **Rate Limiting**: Implement rate limiting để tránh spam
4. **Validation**: Validate dữ liệu đầu vào từ webhook

## Monitoring

1. **Health Check**: Endpoint `/api/zalo-webhook/chatwoot-test`
2. **Metrics**: Theo dõi số lượng message, contact, conversation được tạo
3. **Error Tracking**: Log và alert khi có lỗi API
4. **Performance**: Monitor response time của Chatwoot API

---

## Liên hệ hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra logs chi tiết
2. Test từng bước theo hướng dẫn
3. Liên hệ team support với thông tin logs
