# Tóm tắt tích hợp Chatwoot

## Những gì đã được cập nhật

### 1. <PERSON><PERSON><PERSON> hình <PERSON>woot (`src/config/chatwoot.ts`)
- ✅ Thêm `accountId` vào interface và config
- ✅ Cập nhật validation để kiểm tra `CHATWOOT_ACCOUNT_ID`
- ✅ Hỗ trợ đầy đủ format API của Chatwoot

### 2. ChatwootService (`src/services/ChatwootService.ts`)
- ✅ Cập nhật tất cả API endpoints để sử dụng `account_id` trong URL
- ✅ Thêm logic tìm kiếm contact hiện có để tránh duplicate
- ✅ Cải thiện error handling và logging chi tiết
- ✅ Thêm method `findContactByIdentifier()` để tìm contact theo Zalo UID
- ✅ Cập nhật `createOrFindContact()` để sử dụng logic tìm kiếm trước

### 3. Webhook Routes (`src/routes/zalo-webhook.ts`)
- ✅ Cập nhật endpoint test để hiển thị `accountId`
- ✅ Thêm endpoint `/chatwoot-test-contact` để test tạo contact
- ✅ Thêm endpoint `/chatwoot-test-message` để test flow hoàn chỉnh
- ✅ Thêm endpoint `/chatwoot-config` để xem cấu hình hiện tại

### 4. Environment Configuration (`.env.example`)
- ✅ Thêm `CHATWOOT_ACCOUNT_ID` vào file example
- ✅ Cập nhật comments hướng dẫn cách lấy Account ID
- ✅ Thêm `WEBHOOK_URL` configuration

### 5. Documentation (`docs/CHATWOOT_SETUP.md`)
- ✅ Tạo hướng dẫn chi tiết cách setup API Channel
- ✅ Hướng dẫn lấy Account ID, API Token, Inbox ID
- ✅ Giải thích flow xử lý tin nhắn
- ✅ Troubleshooting guide
- ✅ Bảo mật và monitoring

## API Endpoints mới

### Test Endpoints
1. `GET /api/zalo-webhook/chatwoot-test` - Test kết nối Chatwoot
2. `POST /api/zalo-webhook/chatwoot-test-contact` - Test tạo contact
3. `POST /api/zalo-webhook/chatwoot-test-message` - Test flow hoàn chỉnh
4. `GET /api/zalo-webhook/chatwoot-config` - Xem cấu hình hiện tại

### Test Contact Creation
```bash
POST /api/zalo-webhook/chatwoot-test-contact
Content-Type: application/json

{
  "name": "Test User",
  "identifier": "test-user-123"
}
```

### Test Message Flow
```bash
POST /api/zalo-webhook/chatwoot-test-message
Content-Type: application/json

{
  "name": "Test User",
  "identifier": "test-user-123",
  "content": "Hello from test"
}
```

## Cấu hình cần thiết

### Environment Variables
```env
CHATWOOT_ENABLED=true
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_API_ACCESS_TOKEN=your_token_here
CHATWOOT_ACCOUNT_ID=213
CHATWOOT_INBOX_ID=281
```

### Cách lấy thông tin cần thiết:

1. **Account ID**: Từ URL dashboard
   ```
   https://app.chatwoot.com/app/accounts/213/dashboard
                                    ^^^
                                Account ID
   ```

2. **API Access Token**: Profile Settings → Access Token → Create New Token

3. **Inbox ID**: Settings → Inboxes → API Channel → Configuration

## Flow xử lý tin nhắn

1. **Nhận webhook từ Zalo** → `/api/zalo-webhook`
2. **Tìm/Tạo Contact** → Sử dụng `uidFrom` làm identifier
3. **Tìm/Tạo Conversation** → Sử dụng `source_id` từ contact_inbox
4. **Tạo Message** → Tin nhắn incoming từ user
5. **Gửi webhook response** → Phản hồi tự động

## Cải tiến về Error Handling

### Logging chi tiết
- Status code và response data từ Chatwoot API
- Request data để debug
- Thông tin cấu hình (không bao gồm sensitive data)

### Error Recovery
- Không fail webhook nếu Chatwoot có lỗi
- Log chi tiết để debug
- Graceful degradation

## Tính năng chống duplicate

### Contact Deduplication
- Tìm kiếm contact theo `zalo_uid` trước khi tạo mới
- Sử dụng existing contact nếu tìm thấy
- Tạo contact_inbox nếu chưa có cho inbox hiện tại

### Conversation Management
- Tìm conversation đang mở của contact
- Tái sử dụng conversation hiện có
- Tạo mới nếu không tìm thấy

## Monitoring và Debug

### Health Check
```bash
GET /api/zalo-webhook/chatwoot-test
```

### Configuration Check
```bash
GET /api/zalo-webhook/chatwoot-config
```

### Logs
- Tất cả API calls được log với request/response details
- Error logs bao gồm status code và response data
- Success logs bao gồm created object IDs

## Bước tiếp theo

1. **Cập nhật environment variables** theo `.env.example`
2. **Test kết nối** bằng `/chatwoot-test` endpoint
3. **Test tạo contact** bằng `/chatwoot-test-contact` endpoint
4. **Test flow hoàn chỉnh** bằng `/chatwoot-test-message` endpoint
5. **Gửi tin nhắn thật từ Zalo** để test integration

## Troubleshooting

### Lỗi 401 Unauthorized
- Kiểm tra `CHATWOOT_API_ACCESS_TOKEN`
- Đảm bảo token chưa hết hạn

### Lỗi 404 Not Found
- Kiểm tra `CHATWOOT_ACCOUNT_ID` và `CHATWOOT_INBOX_ID`
- Đảm bảo `CHATWOOT_BASE_URL` đúng

### Lỗi 422 Unprocessable Entity
- Kiểm tra format dữ liệu gửi lên API
- Xem logs chi tiết để debug

### Contact Duplicate
- Logic tìm kiếm đã được implement
- Kiểm tra logs để xem có tìm thấy existing contact không

---

**Lưu ý**: Tất cả các thay đổi đã được implement và sẵn sàng để test. Hãy cập nhật environment variables và test từng bước theo hướng dẫn.
