import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { logger } from './utils/logger';

// Import types
import './types/api';

// Declare Node.js globals
declare const process: any;

// Import routes
import testRoutes from './routes/test';
import zaloWebhookRoutes from './routes/zalo-webhook';
import zaloChatwootMappingRoutes from './routes/zalo-chatwoot-mapping';

// Import middleware
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

class ExpressApp {
  public app: Application;
  private port: number;

  constructor(port: number = 3000) {
    this.app = express();
    this.port = port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://yourdomain.com'] // Thay đổi domain của bạn
        : ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    if (process.env.NODE_ENV !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message: string) => {
            logger.info(message.trim());
          }
        }
      }));
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      req.id = Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.id);
      next();
    });

    // Health check endpoint
    this.app.get('/health', (_req: Request, res: Response) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
      });
    });
  }

  private initializeRoutes(): void {
    // API routes
    this.app.use('/api/test', testRoutes);
    this.app.use('/api/zalo-webhook', zaloWebhookRoutes);
    this.app.use('/api/zalo-chatwoot-mapping', zaloChatwootMappingRoutes);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        message: 'Zalo Chatbot API',
        version: '1.0.0',
        endpoints: {
          health: '/health',
          test: '/api/test',
          zaloWebhook: '/api/zalo-webhook'
        }
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);
    
    // Global error handler
    this.app.use(errorHandler);
  }

  public listen(): void {
    this.app.listen(this.port, () => {
      logger.info(`🚀 Express server is running on port ${this.port}`);
      logger.info(`📍 Health check: http://localhost:${this.port}/health`);
      logger.info(`📍 API docs: http://localhost:${this.port}/api/test`);
      logger.info(`📍 Zalo Webhook: http://localhost:${this.port}/api/zalo-webhook`);
    });
  }

  public getApp(): Application {
    return this.app;
  }
}

export default ExpressApp;
