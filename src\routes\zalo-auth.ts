import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant } from '../middleware/tenantMiddleware';
import {
  validateZaloAccountOwnership,
  validateActiveZaloAccount,
  rateLimitQRGeneration,
  rateLimitAccountOperations,
  validateChatwootIntegration
} from '../middleware/zaloAuthMiddleware';
import { logger } from '../utils/logger';
import { zaloAuthService } from '../services/ZaloAuthService';
import { zaloSessionManager } from '../services/ZaloSessionManager';
import { supabaseAdmin } from '../config/supabase';
import * as fs from 'fs';
import * as path from 'path';

const router = Router();

// POST /api/zalo-auth/generate-qr - Tạo QR code để đăng nhập
router.post('/generate-qr', validateTenant, rateLimitQRGeneration, asyncHandler(async (req: Request, res: Response) => {
  const { expires_in_minutes = 10, user_agent } = req.body;
  const tenantId = req.tenantId!;

  logger.info('Generate QR login request', {
    tenantId,
    expiresInMinutes: expires_in_minutes,
    requestId: req.id
  });

  try {
    const result = await zaloAuthService.generateQRLogin({
      tenant_id: tenantId,
      user_agent,
      expires_in_minutes
    });

    res.json({
      success: true,
      message: 'QR code generated successfully',
      data: {
        account_id: result.account_id,
        qr_session_id: result.qr_session_id,
        expires_at: result.expires_at,
        status: result.status,
        qr_code_url: `/api/zalo-auth/qr-image/${result.qr_session_id}`
      }
    });

  } catch (error: any) {
    logger.error('Failed to generate QR code', {
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to generate QR code: ${error.message}`,
      500,
      'QR_GENERATION_FAILED'
    );
  }
}));

// GET /api/zalo-auth/qr-image/:sessionId - Lấy QR code image
router.get('/qr-image/:sessionId', asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;

  try {
    const qrPath = path.join(process.cwd(), 'qr_codes', `${sessionId}.png`);

    if (!fs.existsSync(qrPath)) {
      throw new AppError('QR code not found or expired', 404, 'QR_NOT_FOUND');
    }

    res.setHeader('Content-Type', 'image/png');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.sendFile(qrPath);

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to serve QR image', {
      sessionId,
      error: error.message
    });

    throw new AppError('Failed to load QR code', 500, 'QR_LOAD_FAILED');
  }
}));

// GET /api/zalo-auth/qr-status/:accountId - Kiểm tra trạng thái QR
router.get('/qr-status/:accountId', validateTenant, validateZaloAccountOwnership, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const result = await zaloAuthService.checkQRStatus(accountId, tenantId);

    res.json({
      success: true,
      message: 'QR status retrieved successfully',
      data: {
        status: result.status,
        account: result.account ? {
          id: result.account.id,
          zalo_user_id: result.account.zalo_user_id,
          zalo_display_name: result.account.zalo_display_name,
          status: result.account.status,
          qr_data: result.account.qr_data
        } : null
      }
    });

  } catch (error: any) {
    logger.error('Failed to check QR status', {
      accountId,
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to check QR status: ${error.message}`,
      500,
      'QR_STATUS_CHECK_FAILED'
    );
  }
}));

// POST /api/zalo-auth/login-cookie - Đăng nhập bằng cookie
router.post('/login-cookie', validateTenant, rateLimitAccountOperations, asyncHandler(async (req: Request, res: Response) => {
  const { cookies, imei, user_agent, zalo_user_id, zalo_display_name } = req.body;
  const tenantId = req.tenantId!;

  // Validate required fields
  if (!cookies || !imei || !user_agent) {
    throw new AppError(
      'Missing required fields: cookies, imei, user_agent',
      400,
      'MISSING_REQUIRED_FIELDS'
    );
  }

  logger.info('Cookie login request', {
    tenantId,
    zaloUserId: zalo_user_id,
    requestId: req.id
  });

  try {
    const account = await zaloAuthService.loginWithCookie({
      tenant_id: tenantId,
      cookies,
      imei,
      user_agent,
      zalo_user_id,
      zalo_display_name
    });

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        account: {
          id: account.id,
          zalo_user_id: account.zalo_user_id,
          zalo_display_name: account.zalo_display_name,
          zalo_phone: account.zalo_phone,
          status: account.status,
          chatwoot_inbox_id: account.chatwoot_inbox_id,
          chatwoot_channel_status: account.chatwoot_channel_status,
          last_login_at: account.last_login_at,
          expires_at: account.expires_at
        }
      }
    });

  } catch (error: any) {
    logger.error('Cookie login failed', {
      tenantId,
      zaloUserId: zalo_user_id,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Login failed: ${error.message}`,
      400,
      'LOGIN_FAILED'
    );
  }
}));

// GET /api/zalo-auth/accounts - Lấy danh sách accounts
router.get('/accounts', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { status, limit = 50, offset = 0 } = req.query;

  try {
    let accounts = await zaloAuthService.getAccountsByTenant(tenantId);

    // Filter by status if provided
    if (status && typeof status === 'string') {
      accounts = accounts.filter(account => account.status === status);
    }

    // Apply pagination
    const total = accounts.length;
    const paginatedAccounts = accounts.slice(
      parseInt(offset as string),
      parseInt(offset as string) + parseInt(limit as string)
    );

    // Get health status for each account
    const accountsWithHealth = paginatedAccounts.map(account => {
      const health = zaloSessionManager.getAccountHealth(account.id!);
      return {
        id: account.id,
        zalo_user_id: account.zalo_user_id,
        zalo_display_name: account.zalo_display_name,
        zalo_phone: account.zalo_phone,
        status: account.status,
        chatwoot_inbox_id: account.chatwoot_inbox_id,
        chatwoot_channel_status: account.chatwoot_channel_status,
        login_method: account.login_method,
        last_login_at: account.last_login_at,
        last_activity_at: account.last_activity_at,
        expires_at: account.expires_at,
        created_at: account.created_at,
        health: health ? {
          is_healthy: health.isHealthy,
          last_check: health.lastCheck,
          error_count: health.errorCount,
          last_error: health.lastError
        } : null,
        error_data: account.error_data
      };
    });

    res.json({
      success: true,
      message: 'Accounts retrieved successfully',
      data: {
        accounts: accountsWithHealth,
        pagination: {
          total,
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          has_more: (parseInt(offset as string) + parseInt(limit as string)) < total
        }
      }
    });

  } catch (error: any) {
    logger.error('Failed to get accounts', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get accounts: ${error.message}`,
      500,
      'GET_ACCOUNTS_FAILED'
    );
  }
}));

// GET /api/zalo-auth/account/:accountId - Lấy chi tiết account
router.get('/account/:accountId', validateTenant, validateZaloAccountOwnership, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const status = await zaloAuthService.getAccountStatus(accountId, tenantId);
    const health = zaloSessionManager.getAccountHealth(accountId);

    res.json({
      success: true,
      message: 'Account status retrieved successfully',
      data: {
        ...status,
        health: health ? {
          is_healthy: health.isHealthy,
          last_check: health.lastCheck,
          error_count: health.errorCount,
          last_error: health.lastError
        } : null
      }
    });

  } catch (error: any) {
    logger.error('Failed to get account status', {
      accountId,
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get account status: ${error.message}`,
      500,
      'GET_ACCOUNT_STATUS_FAILED'
    );
  }
}));

// POST /api/zalo-auth/refresh/:accountId - Refresh session
router.post('/refresh/:accountId', validateTenant, validateZaloAccountOwnership, rateLimitAccountOperations, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  logger.info('Refresh session request', {
    accountId,
    tenantId,
    requestId: req.id
  });

  try {
    const success = await zaloAuthService.refreshSession(accountId, tenantId);

    if (success) {
      const status = await zaloAuthService.getAccountStatus(accountId, tenantId);

      res.json({
        success: true,
        message: 'Session refreshed successfully',
        data: status
      });
    } else {
      throw new AppError('Failed to refresh session', 500, 'REFRESH_FAILED');
    }

  } catch (error: any) {
    logger.error('Failed to refresh session', {
      accountId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to refresh session: ${error.message}`,
      500,
      'REFRESH_SESSION_FAILED'
    );
  }
}));

// DELETE /api/zalo-auth/account/:accountId - Xóa account
router.delete('/account/:accountId', validateTenant, validateZaloAccountOwnership, rateLimitAccountOperations, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  logger.info('Remove account request', {
    accountId,
    tenantId,
    requestId: req.id
  });

  try {
    const success = await zaloAuthService.removeAccount(accountId, tenantId);

    if (success) {
      res.json({
        success: true,
        message: 'Account removed successfully',
        data: {
          account_id: accountId,
          removed_at: new Date().toISOString()
        }
      });
    } else {
      throw new AppError('Failed to remove account', 500, 'REMOVE_FAILED');
    }

  } catch (error: any) {
    logger.error('Failed to remove account', {
      accountId,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to remove account: ${error.message}`,
      500,
      'REMOVE_ACCOUNT_FAILED'
    );
  }
}));

// GET /api/zalo-auth/health - Lấy health status của tất cả sessions
router.get('/health', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    const stats = zaloSessionManager.getSessionStats();
    const allHealth = zaloSessionManager.getAllHealthStatus();

    // Filter health data for current tenant
    const tenantHealth = allHealth.filter(h => h.tenantId === tenantId);

    res.json({
      success: true,
      message: 'Health status retrieved successfully',
      data: {
        stats: {
          ...stats,
          tenant_sessions: tenantHealth.length,
          tenant_healthy: tenantHealth.filter(h => h.isHealthy).length,
          tenant_errors: tenantHealth.filter(h => !h.isHealthy).length
        },
        sessions: tenantHealth.map(h => ({
          account_id: h.accountId,
          zalo_user_id: h.zaloUserId,
          is_healthy: h.isHealthy,
          last_check: h.lastCheck,
          error_count: h.errorCount,
          last_error: h.lastError
        }))
      }
    });

  } catch (error: any) {
    logger.error('Failed to get health status', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get health status: ${error.message}`,
      500,
      'GET_HEALTH_FAILED'
    );
  }
}));

// POST /api/zalo-auth/health-check/:accountId - Force health check
router.post('/health-check/:accountId', validateTenant, validateZaloAccountOwnership, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  try {
    // Get account info first
    const status = await zaloAuthService.getAccountStatus(accountId, tenantId);

    // Force health check
    const health = await zaloSessionManager.forceHealthCheck(
      accountId,
      tenantId,
      status.zalo_user_id
    );

    res.json({
      success: true,
      message: 'Health check completed',
      data: {
        account_id: accountId,
        zalo_user_id: health.zaloUserId,
        is_healthy: health.isHealthy,
        last_check: health.lastCheck,
        error_count: health.errorCount,
        last_error: health.lastError
      }
    });

  } catch (error: any) {
    logger.error('Failed to perform health check', {
      accountId,
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to perform health check: ${error.message}`,
      500,
      'HEALTH_CHECK_FAILED'
    );
  }
}));

// POST /api/zalo-auth/link-chatwoot - Liên kết account với Chatwoot inbox
router.post('/link-chatwoot', validateTenant, validateChatwootIntegration, asyncHandler(async (req: Request, res: Response) => {
  const { account_id, chatwoot_inbox_id, chatwoot_source_id } = req.body;
  const tenantId = req.tenantId!;

  if (!account_id || !chatwoot_inbox_id) {
    throw new AppError(
      'Missing required fields: account_id, chatwoot_inbox_id',
      400,
      'MISSING_REQUIRED_FIELDS'
    );
  }

  logger.info('Link Chatwoot request', {
    accountId: account_id,
    chatwootInboxId: chatwoot_inbox_id,
    tenantId,
    requestId: req.id
  });

  try {
    // Update account with Chatwoot info
    const { data, error } = await supabaseAdmin
      .from('zalo_accounts')
      .update({
        chatwoot_inbox_id,
        chatwoot_source_id: chatwoot_source_id || `zalo_${account_id}`,
        chatwoot_channel_status: 'active'
      })
      .eq('id', account_id)
      .eq('tenant_id', tenantId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to link Chatwoot: ${error.message}`);
    }

    res.json({
      success: true,
      message: 'Chatwoot linked successfully',
      data: {
        account_id: data.id,
        zalo_user_id: data.zalo_user_id,
        chatwoot_inbox_id: data.chatwoot_inbox_id,
        chatwoot_source_id: data.chatwoot_source_id,
        chatwoot_channel_status: data.chatwoot_channel_status
      }
    });

  } catch (error: any) {
    logger.error('Failed to link Chatwoot', {
      accountId: account_id,
      chatwootInboxId: chatwoot_inbox_id,
      tenantId,
      error: error.message,
      requestId: req.id
    });

    throw new AppError(
      `Failed to link Chatwoot: ${error.message}`,
      500,
      'CHATWOOT_LINK_FAILED'
    );
  }
}));

export default router;
