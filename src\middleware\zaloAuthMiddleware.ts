import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { supabaseAdmin } from '../config/supabase';
import { logger } from '../utils/logger';

// Extend Request interface để thêm Zalo account info
declare global {
  namespace Express {
    interface Request {
      zaloAccount?: any;
    }
  }
}

/**
 * Middleware để validate Zalo account ownership
 */
export const validateZaloAccountOwnership = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { accountId } = req.params;
    const tenantId = req.tenantId;

    if (!tenantId) {
      throw new AppError('Tenant ID is required', 400, 'TENANT_ID_REQUIRED');
    }

    if (!accountId) {
      throw new AppError('Account ID is required', 400, 'ACCOUNT_ID_REQUIRED');
    }

    // Set tenant context
    await supabaseAdmin.rpc('set_config', {
      setting_name: 'app.current_tenant_id',
      setting_value: tenantId,
      is_local: true
    });

    // Verify account ownership
    const { data: account, error } = await supabaseAdmin
      .from('zalo_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('tenant_id', tenantId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new AppError('Zalo account not found or access denied', 404, 'ACCOUNT_NOT_FOUND');
      }
      throw new AppError(`Database error: ${error.message}`, 500, 'DATABASE_ERROR');
    }

    // Attach account to request
    req.zaloAccount = account;

    logger.debug('Zalo account ownership validated', {
      accountId,
      tenantId,
      zaloUserId: account.zalo_user_id,
      requestId: req.id
    });

    next();
  } catch (error: any) {
    logger.error('Zalo account ownership validation failed', {
      accountId: req.params.accountId,
      tenantId: req.tenantId,
      error: error.message,
      requestId: req.id
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Account validation failed', 500, 'ACCOUNT_VALIDATION_ERROR'));
    }
  }
};

/**
 * Middleware để validate active Zalo account
 */
export const validateActiveZaloAccount = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Ensure account ownership is validated first
    if (!req.zaloAccount) {
      throw new AppError('Account validation required', 400, 'ACCOUNT_VALIDATION_REQUIRED');
    }

    const account = req.zaloAccount;

    // Check if account is active
    if (account.status !== 'active') {
      throw new AppError(
        `Account is not active. Current status: ${account.status}`,
        400,
        'ACCOUNT_NOT_ACTIVE'
      );
    }

    // Check if account is expired
    if (account.expires_at && new Date(account.expires_at) < new Date()) {
      throw new AppError('Account session has expired', 401, 'ACCOUNT_EXPIRED');
    }

    logger.debug('Active Zalo account validated', {
      accountId: account.id,
      zaloUserId: account.zalo_user_id,
      status: account.status,
      requestId: req.id
    });

    next();
  } catch (error: any) {
    logger.error('Active Zalo account validation failed', {
      accountId: req.zaloAccount?.id,
      error: error.message,
      requestId: req.id
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Active account validation failed', 500, 'ACTIVE_ACCOUNT_VALIDATION_ERROR'));
    }
  }
};

/**
 * Rate limiting cho QR generation
 */
const qrGenerationLimits = new Map<string, { count: number; resetTime: number }>();

export const rateLimitQRGeneration = (req: Request, res: Response, next: NextFunction) => {
  try {
    const tenantId = req.tenantId;
    if (!tenantId) {
      throw new AppError('Tenant ID is required', 400, 'TENANT_ID_REQUIRED');
    }

    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxRequests = 10; // Max 10 QR generations per 15 minutes per tenant

    const key = `qr_gen_${tenantId}`;
    const current = qrGenerationLimits.get(key);

    if (!current || now > current.resetTime) {
      // Reset or initialize
      qrGenerationLimits.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      
      logger.debug('QR generation rate limit initialized', {
        tenantId,
        count: 1,
        maxRequests,
        requestId: req.id
      });
      
      next();
      return;
    }

    if (current.count >= maxRequests) {
      const resetIn = Math.ceil((current.resetTime - now) / 1000 / 60); // minutes
      
      logger.warn('QR generation rate limit exceeded', {
        tenantId,
        count: current.count,
        maxRequests,
        resetInMinutes: resetIn,
        requestId: req.id
      });

      throw new AppError(
        `Rate limit exceeded. Maximum ${maxRequests} QR generations per 15 minutes. Try again in ${resetIn} minutes.`,
        429,
        'RATE_LIMIT_EXCEEDED'
      );
    }

    // Increment count
    current.count++;
    qrGenerationLimits.set(key, current);

    logger.debug('QR generation rate limit check passed', {
      tenantId,
      count: current.count,
      maxRequests,
      requestId: req.id
    });

    next();
  } catch (error: any) {
    logger.error('QR generation rate limiting failed', {
      tenantId: req.tenantId,
      error: error.message,
      requestId: req.id
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Rate limiting failed', 500, 'RATE_LIMITING_ERROR'));
    }
  }
};

/**
 * Rate limiting cho account operations
 */
const accountOperationLimits = new Map<string, { count: number; resetTime: number }>();

export const rateLimitAccountOperations = (req: Request, res: Response, next: NextFunction) => {
  try {
    const tenantId = req.tenantId;
    if (!tenantId) {
      throw new AppError('Tenant ID is required', 400, 'TENANT_ID_REQUIRED');
    }

    const now = Date.now();
    const windowMs = 5 * 60 * 1000; // 5 minutes
    const maxRequests = 30; // Max 30 operations per 5 minutes per tenant

    const key = `account_ops_${tenantId}`;
    const current = accountOperationLimits.get(key);

    if (!current || now > current.resetTime) {
      // Reset or initialize
      accountOperationLimits.set(key, {
        count: 1,
        resetTime: now + windowMs
      });
      next();
      return;
    }

    if (current.count >= maxRequests) {
      const resetIn = Math.ceil((current.resetTime - now) / 1000 / 60); // minutes
      
      logger.warn('Account operations rate limit exceeded', {
        tenantId,
        count: current.count,
        maxRequests,
        resetInMinutes: resetIn,
        requestId: req.id
      });

      throw new AppError(
        `Rate limit exceeded. Maximum ${maxRequests} account operations per 5 minutes. Try again in ${resetIn} minutes.`,
        429,
        'RATE_LIMIT_EXCEEDED'
      );
    }

    // Increment count
    current.count++;
    accountOperationLimits.set(key, current);

    next();
  } catch (error: any) {
    logger.error('Account operations rate limiting failed', {
      tenantId: req.tenantId,
      error: error.message,
      requestId: req.id
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Rate limiting failed', 500, 'RATE_LIMITING_ERROR'));
    }
  }
};

/**
 * Validate Chatwoot integration requirements
 */
export const validateChatwootIntegration = (req: Request, res: Response, next: NextFunction) => {
  try {
    const { chatwoot_inbox_id } = req.body;

    if (!chatwoot_inbox_id || typeof chatwoot_inbox_id !== 'number') {
      throw new AppError(
        'Valid Chatwoot inbox ID is required',
        400,
        'INVALID_CHATWOOT_INBOX_ID'
      );
    }

    // Additional validation can be added here
    // e.g., check if inbox exists, validate permissions, etc.

    logger.debug('Chatwoot integration validation passed', {
      chatwootInboxId: chatwoot_inbox_id,
      tenantId: req.tenantId,
      requestId: req.id
    });

    next();
  } catch (error: any) {
    logger.error('Chatwoot integration validation failed', {
      error: error.message,
      requestId: req.id
    });

    if (error instanceof AppError) {
      next(error);
    } else {
      next(new AppError('Chatwoot integration validation failed', 500, 'CHATWOOT_VALIDATION_ERROR'));
    }
  }
};

/**
 * Cleanup expired rate limit entries periodically
 */
export const cleanupRateLimits = () => {
  const now = Date.now();
  
  // Cleanup QR generation limits
  for (const [key, value] of qrGenerationLimits.entries()) {
    if (now > value.resetTime) {
      qrGenerationLimits.delete(key);
    }
  }
  
  // Cleanup account operation limits
  for (const [key, value] of accountOperationLimits.entries()) {
    if (now > value.resetTime) {
      accountOperationLimits.delete(key);
    }
  }
  
  logger.debug('Rate limit cleanup completed', {
    qrLimitsSize: qrGenerationLimits.size,
    accountLimitsSize: accountOperationLimits.size
  });
};

// Cleanup every 10 minutes
setInterval(cleanupRateLimits, 10 * 60 * 1000);
