import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant, getTenantIdFromRequest } from '../middleware/tenantMiddleware';
import { logger } from '../utils/logger';
import axios from 'axios';
import { ChatwootService } from '../services/ChatwootService';
import { chatwootConfig, validateChatwootConfig } from '../config/chatwoot';
import { zaloAuthService, zaloSessionManager } from '../services';
import { supabaseAdmin } from '../config/supabase';
import '../types/api';

// Declare Node.js globals
declare const process: any;

const router = Router();

// Khởi tạo Chatwoot service
let chatwootService: ChatwootService | null = null;

// Kiểm tra và khởi tạo Chatwoot service nếu được bật
if (chatwootConfig.enabled && validateChatwootConfig()) {
  chatwootService = new ChatwootService(chatwootConfig);
  logger.info('Chatwoot integration enabled', {
    baseUrl: chatwootConfig.baseUrl,
    inboxId: chatwootConfig.inboxId
  });
} else {
  logger.info('Chatwoot integration disabled or misconfigured');
}

// Interface cho tin nhắn Zalo
interface ZaloMessage {
  type: number;
  data: {
    actionId: string;
    msgId: string;
    cliMsgId: string;
    msgType: string;
    uidFrom: string;
    idTo: string;
    dName: string;
    ts: string;
    status: number;
    content: string;
    notify: string;
    ttl: number;
    userId: string;
    uin: string;
    topOut: string;
    topOutTimeOut: string;
    topOutImprTimeOut: string;
    propertyExt: {
      color: number;
      size: number;
      type: number;
      subType: number;
      ext: string;
    };
    paramsExt: {
      countUnread: number;
      containType: number;
      platformType: number;
    };
    cmd: number;
    st: number;
    at: number;
    realMsgId: string;
  };
  threadId: string;
  isSelf: boolean;
}

// Interface cho phản hồi webhook
interface WebhookResponse {
  message: string;
  timestamp: string;
  originalMessage?: any;
}

// POST /api/zalo-webhook - Nhận tin nhắn từ Zalo (Multi-account support)
router.post('/', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const messageData: ZaloMessage = req.body;
  const tenantId = req.tenantId!;

  logger.info('Received Zalo message', {
    requestId: req.id,
    tenantId,
    messageData: JSON.stringify(messageData, null, 2)
  });

  // Validate dữ liệu đầu vào
  if (!messageData || !messageData.data) {
    throw new AppError('Invalid message format', 400, 'INVALID_MESSAGE_FORMAT');
  }

  const { data, threadId, isSelf } = messageData;
  const { content, dName, uidFrom, msgType, ts, idTo } = data;

  // Skip self messages
  if (isSelf) {
    logger.debug('Skipping self message', { requestId: req.id, threadId });
    return res.status(200).json({ success: true, message: 'Self message ignored' });
  }

  // Log thông tin chi tiết tin nhắn
  logger.info('Message details', {
    requestId: req.id,
    tenantId,
    from: dName,
    userId: uidFrom,
    toUserId: idTo,
    content: content,
    messageType: msgType,
    timestamp: ts,
    threadId: threadId
  });

  // Tìm Zalo account tương ứng với tin nhắn này
  let zaloAccount = null;
  try {
    // Set tenant context
    await supabaseAdmin.rpc('set_config', {
      setting_name: 'app.current_tenant_id',
      setting_value: tenantId,
      is_local: true
    });

    // Tìm account dựa trên idTo (account nhận tin nhắn)
    const { data: accounts, error } = await supabaseAdmin
      .from('zalo_accounts')
      .select('*')
      .eq('tenant_id', tenantId)
      .eq('zalo_user_id', idTo)
      .eq('status', 'active')
      .limit(1);

    if (error) {
      logger.error('Failed to find Zalo account', {
        error: error.message,
        tenantId,
        idTo
      });
    } else if (accounts && accounts.length > 0) {
      zaloAccount = accounts[0];
      logger.info('Found matching Zalo account', {
        accountId: zaloAccount.id,
        zaloUserId: zaloAccount.zalo_user_id,
        displayName: zaloAccount.zalo_display_name
      });

      // Update account activity
      await zaloSessionManager.handleZaloMessage(zaloAccount.id, messageData);
    } else {
      logger.warn('No matching Zalo account found', {
        tenantId,
        idTo,
        threadId
      });
    }
  } catch (error: any) {
    logger.error('Error finding Zalo account', {
      error: error.message,
      tenantId,
      idTo
    });
  }

  try {
    // Xử lý tin nhắn với Chatwoot nếu được bật và có account tương ứng
    let chatwootResult = null;
    if (chatwootService && zaloAccount) {
      try {
        logger.info('Processing message with Chatwoot', {
          requestId: req.id,
          accountId: zaloAccount.id,
          chatwootInboxId: zaloAccount.chatwoot_inbox_id,
          from: dName,
          userId: uidFrom
        });

        // Sử dụng chatwoot_inbox_id từ account nếu có
        const chatwootInboxId = zaloAccount.chatwoot_inbox_id;
        if (!chatwootInboxId) {
          logger.warn('No Chatwoot inbox linked to Zalo account', {
            accountId: zaloAccount.id,
            zaloUserId: zaloAccount.zalo_user_id
          });
        } else {
          chatwootResult = await chatwootService.processZaloMessage({
            from: dName,
            content: content,
            userId: uidFrom,
            messageId: data.msgId,
            timestamp: parseInt(ts),
            threadId: threadId,
            tenantId: tenantId,
            zaloAccountId: zaloAccount.id,
            chatwootInboxId: chatwootInboxId
          });

          logger.info('Chatwoot processing successful', {
            requestId: req.id,
            accountId: zaloAccount.id,
            contactId: chatwootResult.contact.id,
            conversationId: chatwootResult.conversation.id,
            messageId: chatwootResult.message.id
          });
        }

      } catch (chatwootError: any) {
        logger.error('Chatwoot processing failed', {
          requestId: req.id,
          accountId: zaloAccount?.id,
          error: chatwootError.message,
          from: dName,
          userId: uidFrom
        });
        // Không throw error để không làm fail webhook
      }
    } else if (!zaloAccount) {
      logger.warn('No Zalo account found for message processing', {
        requestId: req.id,
        tenantId,
        idTo,
        threadId
      });
    }

    // Trả về phản hồi thành công
    res.status(200).json({
      success: true,
      message: 'Message received and processed successfully',
      data: {
        receivedMessage: {
          from: dName,
          content: content,
          messageId: data.msgId,
          timestamp: ts,
          threadId: threadId
        },
        zaloAccount: zaloAccount ? {
          id: zaloAccount.id,
          zalo_user_id: zaloAccount.zalo_user_id,
          zalo_display_name: zaloAccount.zalo_display_name,
          chatwoot_inbox_id: zaloAccount.chatwoot_inbox_id,
          status: 'found'
        } : {
          status: 'not_found',
          message: 'No matching Zalo account found for this message'
        },
        chatwoot: chatwootResult ? {
          enabled: true,
          contactId: chatwootResult.contact.id,
          conversationId: chatwootResult.conversation.id,
          messageId: chatwootResult.message.id,
          status: 'success'
        } : {
          enabled: chatwootConfig.enabled,
          status: zaloAccount ? (zaloAccount.chatwoot_inbox_id ? 'processed' : 'no_inbox_linked') : 'no_account'
        },
        processedAt: new Date().toISOString()
      }
    });

  } catch (processingError: any) {
    logger.error('Failed to process message', {
      requestId: req.id,
      tenantId,
      error: processingError.message,
      threadId,
      from: dName
    });

    // Vẫn trả về thành công cho Zalo để không làm fail webhook
    res.status(200).json({
      success: true,
      message: 'Message received but processing failed',
      data: {
        receivedMessage: {
          from: dName,
          content: content,
          messageId: data.msgId,
          timestamp: ts,
          threadId: threadId
        },
        error: {
          message: processingError.message,
          code: 'PROCESSING_FAILED'
        },
        processedAt: new Date().toISOString()
      }
    });
  }
}));

// GET /api/zalo-webhook/test - Test endpoint để kiểm tra webhook
router.get('/test', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Zalo webhook test endpoint accessed', { requestId: req.id });
  
  res.json({
    success: true,
    message: 'Zalo webhook endpoint is working',
    data: {
      endpoint: '/api/zalo-webhook',
      method: 'POST',
      expectedFormat: {
        type: 0,
        data: {
          actionId: "string",
          msgId: "string",
          cliMsgId: "string",
          msgType: "string",
          uidFrom: "string",
          idTo: "string",
          dName: "string",
          ts: "string",
          status: "number",
          content: "string",
          // ... other fields
        },
        threadId: "string",
        isSelf: "boolean"
      },
      autoReplyMessage: "Đã Nhận",
      webhookUrl: "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982",
      timestamp: new Date().toISOString()
    }
  });
}));

// POST /api/zalo-webhook/test-send - Test gửi webhook thủ công
router.post('/test-send', asyncHandler(async (req: Request, res: Response) => {
  const { message = "Test message from API" } = req.body;
  const webhookUrl = "https://webhook.mooly.vn/webhook/9970c4e7-1891-401a-9182-9e084d435982";

  logger.info('Manual webhook test initiated', { 
    requestId: req.id, 
    message: message 
  });

  const testPayload: WebhookResponse = {
    message: message,
    timestamp: new Date().toISOString(),
    originalMessage: {
      from: "Test User",
      userId: "test-user-id",
      content: "This is a test message",
      messageType: "test",
      threadId: "test-thread-id",
      receivedAt: new Date().toISOString(),
      messageId: "test-msg-id",
      clientMessageId: "test-cli-msg-id"
    }
  };

  try {
    const webhookResponse = await axios.post(webhookUrl, testPayload, {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Zalo-Chatbot-API/1.0.0'
      },
      timeout: 10000
    });

    logger.info('Test webhook sent successfully', {
      requestId: req.id,
      status: webhookResponse.status,
      responseData: webhookResponse.data
    });

    res.json({
      success: true,
      message: 'Test webhook sent successfully',
      data: {
        sentPayload: testPayload,
        webhookResponse: {
          status: webhookResponse.status,
          statusText: webhookResponse.statusText,
          data: webhookResponse.data
        }
      }
    });

  } catch (error: any) {
    logger.error('Test webhook failed', {
      requestId: req.id,
      error: error.message
    });

    throw new AppError(
      `Failed to send test webhook: ${error.message}`, 
      500, 
      'WEBHOOK_TEST_FAILED'
    );
  }
}));

// GET /api/zalo-webhook/chatwoot-test - Test Chatwoot connection
router.get('/chatwoot-test', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot connection test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured',
      data: {
        enabled: chatwootConfig.enabled,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        validation: validateChatwootConfig()
      }
    });
  }

  try {
    const connectionTest = await chatwootService.testConnection();

    res.json({
      success: connectionTest,
      message: connectionTest ? 'Chatwoot connection successful' : 'Chatwoot connection failed',
      data: {
        enabled: true,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        connectionStatus: connectionTest ? 'connected' : 'failed',
        validation: validateChatwootConfig()
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot connection test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Chatwoot connection test failed',
      data: {
        enabled: true,
        error: error.message,
        config: {
          baseUrl: chatwootConfig.baseUrl,
          accountId: chatwootConfig.accountId,
          inboxId: chatwootConfig.inboxId,
          hasApiToken: !!chatwootConfig.apiAccessToken
        },
        validation: validateChatwootConfig()
      }
    });
  }
}));

// POST /api/zalo-webhook/chatwoot-test-contact - Test tạo contact
router.post('/chatwoot-test-contact', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot contact creation test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured',
      data: {
        enabled: chatwootConfig.enabled
      }
    });
  }

  const { name = 'Test User', identifier = 'test-user-' + Date.now() } = req.body;

  try {
    logger.info('Testing contact creation', {
      requestId: req.id,
      name,
      identifier
    });

    const { contact, sourceId } = await chatwootService.createOrFindContact({
      name,
      identifier,
      phone_number: '+***********',
      email: '<EMAIL>'
    });

    res.json({
      success: true,
      message: 'Contact created/found successfully',
      data: {
        contact: {
          id: contact.id,
          name: contact.name,
          email: contact.email,
          phone_number: contact.phone_number
        },
        sourceId,
        contactInboxes: contact.contact_inboxes?.length || 0
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot contact creation test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Contact creation test failed',
      data: {
        error: error.message,
        name,
        identifier
      }
    });
  }
}));

// POST /api/zalo-webhook/chatwoot-test-message - Test tạo message hoàn chỉnh
router.post('/chatwoot-test-message', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot message creation test endpoint accessed', { requestId: req.id });

  if (!chatwootService) {
    return res.json({
      success: false,
      message: 'Chatwoot integration is disabled or misconfigured'
    });
  }

  const {
    name = 'Test User',
    identifier = 'test-user-' + Date.now(),
    content = 'This is a test message from API',
    threadId = 'test-thread-' + Date.now()
  } = req.body;

  try {
    logger.info('Testing full message flow', {
      requestId: req.id,
      name,
      identifier,
      content,
      threadId
    });

    // Lấy tenantId từ middleware (đã được validate)
    const tenantId = req.tenantId!;

    const result = await chatwootService.processZaloMessage({
      from: name,
      content,
      userId: identifier,
      messageId: 'test-msg-' + Date.now(),
      timestamp: Date.now(),
      threadId: threadId,
      tenantId: tenantId
    });

    res.json({
      success: true,
      message: 'Message flow test successful',
      data: {
        contact: {
          id: result.contact.id,
          name: result.contact.name
        },
        conversation: {
          id: result.conversation.id,
          status: result.conversation.status
        },
        message: {
          id: result.message.id,
          content: result.message.content,
          message_type: result.message.message_type
        }
      }
    });

  } catch (error: any) {
    logger.error('Chatwoot message creation test failed', {
      requestId: req.id,
      error: error.message
    });

    res.json({
      success: false,
      message: 'Message flow test failed',
      data: {
        error: error.message,
        name,
        identifier,
        content,
        threadId
      }
    });
  }
}));

// GET /api/zalo-webhook/chatwoot-config - Hiển thị cấu hình hiện tại
router.get('/chatwoot-config', asyncHandler(async (req: Request, res: Response) => {
  logger.info('Chatwoot config endpoint accessed', { requestId: req.id });

  const configStatus = {
    enabled: chatwootConfig.enabled,
    baseUrl: chatwootConfig.baseUrl,
    accountId: chatwootConfig.accountId,
    inboxId: chatwootConfig.inboxId,
    hasApiToken: !!chatwootConfig.apiAccessToken,
    validation: validateChatwootConfig(),
    serviceInitialized: !!chatwootService
  };

  res.json({
    success: true,
    message: 'Chatwoot configuration status',
    data: configStatus
  });
}));

export default router;
