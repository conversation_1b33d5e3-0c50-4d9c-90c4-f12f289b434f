// Declare Node.js globals
declare const process: any;

export interface ChatwootConfig {
  baseUrl: string;
  apiAccessToken: string;
  accountId: number;
  inboxId: number;
  enabled: boolean;
}

export const chatwootConfig: ChatwootConfig = {
  // URL của Chatwoot instance (ví dụ: https://app.chatwoot.com hoặc self-hosted URL)
  baseUrl: process.env.CHATWOOT_BASE_URL || 'https://app.chatwoot.com',

  // API Access Token từ Profile Settings → Access Token
  apiAccessToken: process.env.CHATWOOT_API_ACCESS_TOKEN || '',

  // Account ID từ URL dashboard (ví dụ: /app/accounts/213/dashboard)
  accountId: parseInt(process.env.CHATWOOT_ACCOUNT_ID || '0'),

  // Inbox ID của API channel đã tạo
  inboxId: parseInt(process.env.CHATWOOT_INBOX_ID || '0'),

  // Bật/tắt tích hợp Chatwoot
  enabled: true
};

// Validate config
export function validateChatwootConfig(): boolean {
  if (!chatwootConfig.enabled) {
    return true; // OK nếu disabled
  }

  const errors: string[] = [];

  if (!chatwootConfig.baseUrl) {
    errors.push('CHATWOOT_BASE_URL is required');
  }

  if (!chatwootConfig.apiAccessToken) {
    errors.push('CHATWOOT_API_ACCESS_TOKEN is required');
  }

  if (!chatwootConfig.accountId || chatwootConfig.accountId === 0) {
    errors.push('CHATWOOT_ACCOUNT_ID is required and must be a valid number');
  }

  if (!chatwootConfig.inboxId || chatwootConfig.inboxId === 0) {
    errors.push('CHATWOOT_INBOX_ID is required and must be a valid number');
  }

  if (errors.length > 0) {
    console.error('Chatwoot configuration errors:', errors);
    return false;
  }

  return true;
}
