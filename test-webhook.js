const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

// Sample Zalo message data
const sampleZaloMessage = {
  type: 0,
  data: {
    actionId: "10800571650848",
    msgId: "6824012833145",
    cliMsgId: "1753014376324",
    msgType: "webchat",
    uidFrom: "4771680556510128931",
    idTo: "1775737296024628671",
    dName: "Thọ Trần",
    ts: "1753014374857",
    status: 1,
    content: "alo",
    notify: "1",
    ttl: 0,
    userId: "0",
    uin: "0",
    topOut: "0",
    topOutTimeOut: "0",
    topOutImprTimeOut: "0",
    propertyExt: {
      color: 0,
      size: 0,
      type: 0,
      subType: 0,
      ext: "{\"shouldParseLinkOrContact\": 0}"
    },
    paramsExt: {
      countUnread: 1,
      containType: 0,
      platformType: 2
    },
    cmd: 501,
    st: 3,
    at: 5,
    realMsgId: "0"
  },
  threadId: "4771680556510128931",
  isSelf: false
};

async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', response.data);
  } catch (error) {
    console.error('❌ Health Check failed:', error.message);
  }
}

async function testWebhookEndpoint() {
  console.log('\n🔍 Testing Webhook Test Endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/api/zalo-webhook/test`);
    console.log('✅ Webhook Test:', response.data);
  } catch (error) {
    console.error('❌ Webhook Test failed:', error.message);
  }
}

async function testManualWebhook() {
  console.log('\n🔍 Testing Manual Webhook Send...');
  try {
    const response = await axios.post(`${BASE_URL}/api/zalo-webhook/test-send`, {
      message: "Test message from script"
    });
    console.log('✅ Manual Webhook:', response.data);
  } catch (error) {
    console.error('❌ Manual Webhook failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

async function testZaloMessage() {
  console.log('\n🔍 Testing Zalo Message Processing...');
  try {
    const response = await axios.post(`${BASE_URL}/api/zalo-webhook`, sampleZaloMessage);
    console.log('✅ Zalo Message:', response.data);
  } catch (error) {
    console.error('❌ Zalo Message failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

async function testWithCustomMessage(content) {
  console.log(`\n🔍 Testing with custom message: "${content}"`);
  const customMessage = {
    ...sampleZaloMessage,
    data: {
      ...sampleZaloMessage.data,
      content: content,
      msgId: Date.now().toString(),
      cliMsgId: Date.now().toString(),
      ts: Date.now().toString()
    }
  };

  try {
    const response = await axios.post(`${BASE_URL}/api/zalo-webhook`, customMessage);
    console.log('✅ Custom Message:', response.data);
  } catch (error) {
    console.error('❌ Custom Message failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

async function runAllTests() {
  console.log('🚀 Starting Webhook API Tests...');
  console.log('=====================================');

  await testHealthCheck();
  await testWebhookEndpoint();
  await testManualWebhook();
  await testZaloMessage();
  await testWithCustomMessage('Hello from test script!');
  await testWithCustomMessage('Xin chào từ script test!');

  console.log('\n=====================================');
  console.log('✨ All tests completed!');
}

// Check command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  runAllTests();
} else {
  const command = args[0];
  
  switch (command) {
    case 'health':
      testHealthCheck();
      break;
    case 'webhook-test':
      testWebhookEndpoint();
      break;
    case 'manual':
      testManualWebhook();
      break;
    case 'zalo':
      testZaloMessage();
      break;
    case 'custom':
      const message = args[1] || 'Default custom message';
      testWithCustomMessage(message);
      break;
    default:
      console.log('Available commands:');
      console.log('  node test-webhook.js              - Run all tests');
      console.log('  node test-webhook.js health        - Test health check');
      console.log('  node test-webhook.js webhook-test  - Test webhook endpoint');
      console.log('  node test-webhook.js manual        - Test manual webhook');
      console.log('  node test-webhook.js zalo          - Test Zalo message');
      console.log('  node test-webhook.js custom "msg"  - Test with custom message');
  }
}
