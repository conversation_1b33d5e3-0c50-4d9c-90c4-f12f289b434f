# Hướng dẫn tích hợp Chatwoot API Channel

## Tổng quan

Tài liệu này hướng dẫn cách tích hợp Chatwoot API Channel với hệ thống webhook Zalo để tự động tạo contact, conversation và message trong Chatwoot khi nhận tin nhắn từ Zalo.

## Bước 1: Tạo API Channel trong Chatwoot

### 1.1. <PERSON><PERSON><PERSON> cập Chatwoot Dashboard
- Đăng nhập vào Chatwoot instance của bạn
- Ví dụ: `https://app.chatwoot.com` hoặc self-hosted URL

### 1.2. Tạo API Channel Inbox
1. Vào **Settings** → **Inboxes** → **"Add Inbox"**
2. Chọn **"API"** icon
3. Điền thông tin:
   - **Channel Name**: `Zalo Webhook` (hoặc tên bạn muốn)
   - **Callback URL**: `http://your-domain.com/api/zalo-webhook` (URL webhook của bạn)
4. Click **"Create API Channel"**
5. **Add agents** vào inbox này

### 1.3. <PERSON><PERSON><PERSON> thông tin cần thiết
Sau khi tạo xong, vào **Settings** → **Inboxes** → chọn API channel vừa tạo → **Settings** → **Configuration**:

- **Inbox ID**: Ghi lại số ID này (ví dụ: 123)
- **Inbox Identifier**: Cũng cần lưu lại để dùng cho Client APIs

## Bước 2: Lấy API Access Token

1. Vào **Profile Settings** (click avatar góc phải)
2. Chọn **"Access Token"**
3. Copy **API Access Token** (dạng: `your_api_access_token_here`)

## Bước 3: Cấu hình Environment Variables

Tạo file `.env` từ `.env.example` và cập nhật:

```bash
# Chatwoot Integration
CHATWOOT_ENABLED=true
CHATWOOT_BASE_URL=https://app.chatwoot.com
CHATWOOT_API_ACCESS_TOKEN=your_api_access_token_here
CHATWOOT_INBOX_ID=123
```

**Lưu ý:**
- `CHATWOOT_BASE_URL`: URL của Chatwoot instance
- `CHATWOOT_API_ACCESS_TOKEN`: Token từ Profile Settings
- `CHATWOOT_INBOX_ID`: ID của API channel inbox
- `CHATWOOT_ENABLED`: Set `true` để bật tích hợp

## Bước 4: Test tích hợp

### 4.1. Khởi động server
```bash
yarn dev
```

### 4.2. Test Chatwoot connection
```bash
# PowerShell
Invoke-RestMethod -Uri "http://localhost:3000/api/zalo-webhook/chatwoot-test" -Method Get

# hoặc truy cập browser
http://localhost:3000/api/zalo-webhook/chatwoot-test
```

Kết quả mong đợi:
```json
{
  "success": true,
  "message": "Chatwoot connection successful",
  "data": {
    "enabled": true,
    "config": {
      "baseUrl": "https://app.chatwoot.com",
      "inboxId": 123,
      "hasApiToken": true
    },
    "connectionStatus": "connected"
  }
}
```

### 4.3. Test với tin nhắn Zalo mẫu
```bash
# PowerShell
$body = @{
  type = 0
  data = @{
    actionId = "10800571650848"
    msgId = "6824012833145"
    cliMsgId = "1753014376324"
    msgType = "webchat"
    uidFrom = "4771680556510128931"
    idTo = "1775737296024628671"
    dName = "Test User"
    ts = "1753014374857"
    status = 1
    content = "Hello from Zalo test"
    notify = "1"
    ttl = 0
    userId = "0"
    uin = "0"
    topOut = "0"
    topOutTimeOut = "0"
    topOutImprTimeOut = "0"
    propertyExt = @{
      color = 0
      size = 0
      type = 0
      subType = 0
      ext = '{"shouldParseLinkOrContact": 0}'
    }
    paramsExt = @{
      countUnread = 1
      containType = 0
      platformType = 2
    }
    cmd = 501
    st = 3
    at = 5
    realMsgId = "0"
  }
  threadId = "4771680556510128931"
  isSelf = $false
} | ConvertTo-Json -Depth 10

Invoke-RestMethod -Uri "http://localhost:3000/api/zalo-webhook" -Method Post -ContentType "application/json" -Body $body
```

## Bước 5: Kiểm tra kết quả trong Chatwoot

Sau khi gửi test message thành công:

1. Vào Chatwoot Dashboard
2. Kiểm tra **Conversations** → sẽ thấy conversation mới
3. Kiểm tra **Contacts** → sẽ thấy contact mới được tạo với:
   - **Name**: Tên từ Zalo (dName)
   - **Additional Attributes**: 
     - `zalo_uid`: ID người dùng Zalo
     - `source`: "zalo_webhook"

## Cách hoạt động của hệ thống

### Flow xử lý tin nhắn:

1. **Nhận tin nhắn từ Zalo** → Webhook endpoint `/api/zalo-webhook`

2. **Xử lý Chatwoot** (nếu enabled):
   - Tạo/tìm **Contact** với `zalo_uid`
   - Tìm **Conversation** hiện có hoặc tạo mới
   - Tạo **Message** loại "incoming"

3. **Gửi auto-reply** → Webhook Mooly

4. **Trả response** với thông tin đầy đủ

### Response format với Chatwoot:
```json
{
  "success": true,
  "message": "Message received and processed successfully",
  "data": {
    "receivedMessage": { ... },
    "autoReply": { ... },
    "webhookResponse": { ... },
    "chatwoot": {
      "enabled": true,
      "contactId": 123,
      "conversationId": 456,
      "messageId": 789,
      "status": "success"
    }
  }
}
```

## Troubleshooting

### Lỗi thường gặp:

1. **"Chatwoot integration is disabled"**
   - Kiểm tra `CHATWOOT_ENABLED=true`
   - Kiểm tra các env variables khác

2. **"Chatwoot connection failed"**
   - Kiểm tra `CHATWOOT_BASE_URL` đúng format
   - Kiểm tra `CHATWOOT_API_ACCESS_TOKEN` hợp lệ
   - Kiểm tra network connection

3. **"Contact inbox not found"**
   - Kiểm tra `CHATWOOT_INBOX_ID` đúng
   - Đảm bảo inbox tồn tại và là API channel

4. **"Failed to create conversation"**
   - Kiểm tra contact đã được tạo thành công
   - Kiểm tra source_id hợp lệ

### Debug logs:
Tất cả hoạt động được log chi tiết. Kiểm tra console để debug:
- Contact creation/finding
- Conversation creation
- Message creation
- API errors với response data

## API Endpoints mới

- `GET /api/zalo-webhook/chatwoot-test` - Test Chatwoot connection
- `POST /api/zalo-webhook` - Nhận tin nhắn (đã cập nhật với Chatwoot)
- `GET /api/zalo-webhook/test` - Test webhook endpoint

## Tính năng nâng cao

### 1. HMAC Authentication (tùy chọn)
Nếu cần bảo mật cao hơn, có thể implement HMAC authentication theo tài liệu Chatwoot.

### 2. WebSocket real-time updates
Có thể tích hợp WebSocket để nhận real-time updates từ Chatwoot.

### 3. Client APIs
Sử dụng Client APIs để build custom chat interface.

## Kết luận

Sau khi setup xong, hệ thống sẽ:
- ✅ Nhận tin nhắn từ Zalo webhook
- ✅ Tự động tạo contact trong Chatwoot
- ✅ Tạo conversation và message
- ✅ Gửi auto-reply đến Mooly webhook
- ✅ Log đầy đủ cho monitoring và debug

Agents trong Chatwoot có thể trả lời tin nhắn và quản lý conversations như bình thường.
