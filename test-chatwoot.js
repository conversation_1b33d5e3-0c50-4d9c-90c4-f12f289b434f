const axios = require('axios');

async function testChatwootConnection() {
  try {
    console.log('Testing Chatwoot connection...');
    const response = await axios.get('http://localhost:3000/api/zalo-webhook/chatwoot-test');
    console.log('Connection test result:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Connection test failed:', error.response?.data || error.message);
  }
}

async function testCreateContact() {
  try {
    console.log('\nTesting contact creation...');
    const response = await axios.post('http://localhost:3000/api/zalo-webhook/chatwoot-test-contact', {
      name: 'Test User',
      identifier: 'test-user-' + Date.now()
    });
    console.log('Contact creation result:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Contact creation failed:', error.response?.data || error.message);
  }
}

async function testMessageFlow() {
  try {
    console.log('\nTesting message flow...');
    const response = await axios.post('http://localhost:3000/api/zalo-webhook/chatwoot-test-message', {
      name: 'Test User Flow',
      identifier: 'test-flow-' + Date.now(),
      content: 'Hello from test script!'
    });
    console.log('Message flow result:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('Message flow failed:', error.response?.data || error.message);
  }
}

async function main() {
  await testChatwootConnection();
  await testCreateContact();
  await testMessageFlow();
}

main().catch(console.error);
