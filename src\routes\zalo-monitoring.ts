import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { AppError } from '../middleware/errorHandler';
import { validateTenant } from '../middleware/tenantMiddleware';
import { logger } from '../utils/logger';
import { zaloMonitoringService } from '../services/ZaloMonitoringService';
import { zaloSessionManager } from '../services/ZaloSessionManager';

const router = Router();

// GET /api/zalo-monitoring/metrics - Lấy metrics tổng quan
router.get('/metrics', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    const globalMetrics = zaloMonitoringService.getMetrics();
    const tenantMetrics = zaloMonitoringService.getMetricsByTenant(tenantId);
    const sessionStats = zaloSessionManager.getSessionStats();

    res.json({
      success: true,
      message: 'Monitoring metrics retrieved successfully',
      data: {
        global: globalMetrics,
        tenant: tenantMetrics.metrics,
        sessions: sessionStats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get monitoring metrics', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get monitoring metrics: ${error.message}`,
      500,
      'MONITORING_METRICS_FAILED'
    );
  }
}));

// GET /api/zalo-monitoring/accounts - Lấy metrics của tất cả accounts
router.get('/accounts', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;
  const { status, healthy } = req.query;

  try {
    let accounts = zaloMonitoringService.getMetricsByTenant(tenantId).accounts;

    // Filter by status if provided
    if (status && typeof status === 'string') {
      accounts = accounts.filter(account => account.status === status);
    }

    // Filter by health if provided
    if (healthy !== undefined) {
      const isHealthy = healthy === 'true';
      accounts = accounts.filter(account => account.isHealthy === isHealthy);
    }

    // Sort by last activity (most recent first)
    accounts.sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());

    res.json({
      success: true,
      message: 'Account metrics retrieved successfully',
      data: {
        accounts: accounts.map(account => ({
          account_id: account.accountId,
          zalo_user_id: account.zaloUserId,
          status: account.status,
          is_healthy: account.isHealthy,
          last_activity: account.lastActivity,
          response_time: account.responseTime,
          error_count: account.errorCount,
          uptime: account.uptime,
          chatwoot_status: account.chatwootStatus
        })),
        total: accounts.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get account metrics', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get account metrics: ${error.message}`,
      500,
      'ACCOUNT_METRICS_FAILED'
    );
  }
}));

// GET /api/zalo-monitoring/account/:accountId - Lấy metrics của account cụ thể
router.get('/account/:accountId', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const { accountId } = req.params;
  const tenantId = req.tenantId!;

  try {
    const accountMetrics = zaloMonitoringService.getAccountMetrics(accountId);
    
    if (!accountMetrics) {
      throw new AppError('Account metrics not found', 404, 'ACCOUNT_METRICS_NOT_FOUND');
    }

    // Verify tenant ownership
    if (accountMetrics.tenantId !== tenantId) {
      throw new AppError('Access denied', 403, 'ACCESS_DENIED');
    }

    // Get additional health info from session manager
    const health = zaloSessionManager.getAccountHealth(accountId);

    res.json({
      success: true,
      message: 'Account metrics retrieved successfully',
      data: {
        account_id: accountMetrics.accountId,
        zalo_user_id: accountMetrics.zaloUserId,
        status: accountMetrics.status,
        is_healthy: accountMetrics.isHealthy,
        last_activity: accountMetrics.lastActivity,
        response_time: accountMetrics.responseTime,
        error_count: accountMetrics.errorCount,
        uptime: accountMetrics.uptime,
        chatwoot_status: accountMetrics.chatwootStatus,
        health_details: health ? {
          last_check: health.lastCheck,
          last_error: health.lastError
        } : null,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    if (error instanceof AppError) {
      throw error;
    }

    logger.error('Failed to get account metrics', {
      accountId,
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get account metrics: ${error.message}`,
      500,
      'ACCOUNT_METRICS_FAILED'
    );
  }
}));

// GET /api/zalo-monitoring/dashboard - Lấy dashboard data
router.get('/dashboard', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    const tenantData = zaloMonitoringService.getMetricsByTenant(tenantId);
    const sessionStats = zaloSessionManager.getSessionStats();
    
    // Calculate additional dashboard metrics
    const accounts = tenantData.accounts;
    const recentActivity = accounts.filter(account => {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      return account.lastActivity > fiveMinutesAgo;
    }).length;

    const criticalAccounts = accounts.filter(account => 
      account.status === 'error' || (!account.isHealthy && account.errorCount > 2)
    );

    const performanceMetrics = {
      averageUptime: accounts.length > 0 
        ? accounts.reduce((sum, a) => sum + a.uptime, 0) / accounts.length 
        : 0,
      averageResponseTime: tenantData.metrics.averageResponseTime || 0,
      recentActivity,
      criticalAccounts: criticalAccounts.length
    };

    res.json({
      success: true,
      message: 'Dashboard data retrieved successfully',
      data: {
        overview: {
          total_accounts: tenantData.metrics.totalAccounts || 0,
          active_accounts: tenantData.metrics.activeAccounts || 0,
          healthy_connections: tenantData.metrics.healthyConnections || 0,
          error_accounts: tenantData.metrics.errorAccounts || 0
        },
        performance: performanceMetrics,
        session_stats: sessionStats,
        critical_accounts: criticalAccounts.map(account => ({
          account_id: account.accountId,
          zalo_user_id: account.zaloUserId,
          status: account.status,
          error_count: account.errorCount,
          last_activity: account.lastActivity
        })),
        recent_accounts: accounts
          .filter(account => account.lastActivity > new Date(Date.now() - 30 * 60 * 1000))
          .slice(0, 10)
          .map(account => ({
            account_id: account.accountId,
            zalo_user_id: account.zaloUserId,
            status: account.status,
            is_healthy: account.isHealthy,
            last_activity: account.lastActivity
          })),
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to get dashboard data', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to get dashboard data: ${error.message}`,
      500,
      'DASHBOARD_DATA_FAILED'
    );
  }
}));

// POST /api/zalo-monitoring/refresh - Force refresh metrics
router.post('/refresh', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  const tenantId = req.tenantId!;

  try {
    logger.info('Force refreshing monitoring metrics', { tenantId });

    await zaloMonitoringService.forceUpdateMetrics();

    const updatedMetrics = zaloMonitoringService.getMetricsByTenant(tenantId);

    res.json({
      success: true,
      message: 'Metrics refreshed successfully',
      data: {
        metrics: updatedMetrics.metrics,
        refreshed_at: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Failed to refresh metrics', {
      tenantId,
      error: error.message
    });

    throw new AppError(
      `Failed to refresh metrics: ${error.message}`,
      500,
      'METRICS_REFRESH_FAILED'
    );
  }
}));

// GET /api/zalo-monitoring/alerts - Lấy alert configuration
router.get('/alerts', validateTenant, asyncHandler(async (req: Request, res: Response) => {
  try {
    // For now, return default alert config
    // In production, this could be stored per tenant
    const alertConfig = {
      enabled: true,
      thresholds: {
        error_rate: 20,
        response_time: 5000,
        downtime: 10,
        unhealthy_accounts: 3
      },
      notifications: {
        webhook_enabled: !!process.env.MONITORING_WEBHOOK_URL,
        slack_enabled: !!process.env.SLACK_WEBHOOK_URL
      }
    };

    res.json({
      success: true,
      message: 'Alert configuration retrieved successfully',
      data: alertConfig
    });

  } catch (error: any) {
    logger.error('Failed to get alert configuration', {
      error: error.message
    });

    throw new AppError(
      `Failed to get alert configuration: ${error.message}`,
      500,
      'ALERT_CONFIG_FAILED'
    );
  }
}));

// GET /api/zalo-monitoring/health - Health check endpoint
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  try {
    const globalMetrics = zaloMonitoringService.getMetrics();
    const sessionStats = zaloSessionManager.getSessionStats();

    const isHealthy = globalMetrics.totalAccounts === 0 || 
      (globalMetrics.healthyConnections / globalMetrics.totalAccounts) > 0.8;

    res.status(isHealthy ? 200 : 503).json({
      success: true,
      message: isHealthy ? 'System is healthy' : 'System has issues',
      data: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        uptime: globalMetrics.uptime,
        total_accounts: globalMetrics.totalAccounts,
        healthy_connections: globalMetrics.healthyConnections,
        error_rate: globalMetrics.totalAccounts > 0 
          ? (globalMetrics.errorAccounts / globalMetrics.totalAccounts) * 100 
          : 0,
        average_response_time: globalMetrics.averageResponseTime,
        session_stats: sessionStats,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error('Health check failed', { error: error.message });

    res.status(503).json({
      success: false,
      message: 'Health check failed',
      error: {
        code: 'HEALTH_CHECK_FAILED',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
}));

export default router;
