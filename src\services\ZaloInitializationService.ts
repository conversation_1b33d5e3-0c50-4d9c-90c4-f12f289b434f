import { logger } from '../utils/logger';
import { zaloAuthService } from './ZaloAuthService';
import { zaloSessionManager } from './ZaloSessionManager';
import { zaloMonitoringService } from './ZaloMonitoringService';

export class ZaloInitializationService {
  private isInitialized: boolean = false;
  private initializationPromise: Promise<void> | null = null;

  /**
   * Khởi tạo tất cả Zalo services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<void> {
    try {
      logger.info('Starting Zalo services initialization');

      // 1. Initialize authentication service (loads existing accounts)
      logger.info('Initializing Zalo authentication service');
      // ZaloAuthService auto-initializes in constructor

      // 2. Initialize session manager (starts health monitoring)
      logger.info('Initializing Zalo session manager');
      // ZaloSessionManager auto-starts in constructor

      // 3. Initialize monitoring service
      logger.info('Initializing Zalo monitoring service');
      // ZaloMonitoringService auto-starts in constructor

      // 4. Wait a bit for services to settle
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 5. Perform initial cleanup
      logger.info('Performing initial cleanup');
      await zaloAuthService.cleanupExpiredData();

      // 6. Force initial metrics update
      logger.info('Updating initial metrics');
      await zaloMonitoringService.forceUpdateMetrics();

      this.isInitialized = true;
      logger.info('Zalo services initialization completed successfully');

    } catch (error: any) {
      logger.error('Failed to initialize Zalo services', { error: error.message });
      this.initializationPromise = null;
      throw error;
    }
  }

  /**
   * Graceful shutdown của tất cả services
   */
  async shutdown(): Promise<void> {
    try {
      logger.info('Shutting down Zalo services');

      // Stop monitoring
      zaloMonitoringService.stopMonitoring();

      // Stop session manager
      zaloSessionManager.stopHealthMonitoring();

      // Cleanup any remaining resources
      // (ZaloAuthService doesn't need explicit cleanup)

      this.isInitialized = false;
      logger.info('Zalo services shutdown completed');

    } catch (error: any) {
      logger.error('Error during Zalo services shutdown', { error: error.message });
    }
  }

  /**
   * Health check cho tất cả services
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: {
      auth: boolean;
      session: boolean;
      monitoring: boolean;
    };
    details: any;
  }> {
    try {
      const authHealthy = true; // ZaloAuthService is always healthy if initialized
      const sessionStats = zaloSessionManager.getSessionStats();
      const monitoringMetrics = zaloMonitoringService.getMetrics();

      const sessionHealthy = sessionStats.totalSessions === 0 || 
        (sessionStats.activeSessions / sessionStats.totalSessions) > 0.5;

      const monitoringHealthy = monitoringMetrics.totalAccounts === 0 ||
        (monitoringMetrics.healthyConnections / monitoringMetrics.totalAccounts) > 0.7;

      const allHealthy = authHealthy && sessionHealthy && monitoringHealthy;

      return {
        status: allHealthy ? 'healthy' : 'unhealthy',
        services: {
          auth: authHealthy,
          session: sessionHealthy,
          monitoring: monitoringHealthy
        },
        details: {
          initialized: this.isInitialized,
          sessionStats,
          monitoringMetrics: {
            totalAccounts: monitoringMetrics.totalAccounts,
            activeAccounts: monitoringMetrics.activeAccounts,
            healthyConnections: monitoringMetrics.healthyConnections,
            averageResponseTime: monitoringMetrics.averageResponseTime,
            uptime: monitoringMetrics.uptime
          }
        }
      };

    } catch (error: any) {
      logger.error('Health check failed', { error: error.message });
      return {
        status: 'unhealthy',
        services: {
          auth: false,
          session: false,
          monitoring: false
        },
        details: {
          error: error.message,
          initialized: this.isInitialized
        }
      };
    }
  }

  /**
   * Restart tất cả services
   */
  async restart(): Promise<void> {
    logger.info('Restarting Zalo services');
    
    await this.shutdown();
    
    // Reset initialization state
    this.isInitialized = false;
    this.initializationPromise = null;
    
    await this.initialize();
    
    logger.info('Zalo services restarted successfully');
  }

  /**
   * Get initialization status
   */
  getStatus(): {
    initialized: boolean;
    initializing: boolean;
  } {
    return {
      initialized: this.isInitialized,
      initializing: this.initializationPromise !== null && !this.isInitialized
    };
  }
}

// Export singleton instance
export const zaloInitializationService = new ZaloInitializationService();

// Auto-initialize when module is loaded (optional)
// Uncomment if you want auto-initialization
// zaloInitializationService.initialize().catch(error => {
//   logger.error('Auto-initialization failed', { error: error.message });
// });
